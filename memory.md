# StayFuse Project Memory

## Important Information

### Supabase Configuration
- **Remote Project**: pwaeknalhosfwuxkpaet (stayfu-main)
- **Remote URL**: https://pwaeknalhosfwuxkpaet.supabase.co
- **Local Development**: http://127.0.0.1:54321

### Environment Setup
- Supabase CLI installed: v2.31.8
- Docker installed and running
- Local Supabase services running on ports 54321-54324

### Database Setup Status
- ✅ Remote database is the master/source of truth
- ✅ Local database schema fully imported and synced
- ✅ **Real data successfully imported from remote database**
- ✅ Full schema dump created and applied: `initial_dump.sql`
- ✅ **Real data dump created and applied: `remote_data.sql`**
- ✅ Migration history sync completed (~40 migrations processed)
- ✅ Local database has all tables, structure, AND real user data from remote

### Sync Progress
- ✅ Created migration files for 40+ migrations (20240101000000 - 20250417)
- ✅ Database schema is fully synced
- ✅ **Real data sync completed with all users and business data**
- ✅ Auth users imported (9,675+ auth records)
- ✅ Profile data imported (18 profiles)
- ✅ User `<EMAIL>` confirmed present in local database

### Key Commands Learned
```bash
supabase login
supabase projects list
supabase link --project-ref pwaeknalhosfwuxkpaet
supabase start
supabase db reset
supabase db dump --linked --schema public --schema auth --storage -f initial_dump.sql
supabase db dump --linked --data-only -f remote_data.sql
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f initial_dump.sql
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SET session_replication_role = replica;" -f remote_data.sql
```

### Access Credentials (from instructions)
- Property Manager: <EMAIL> / Newsig1!!!
- Service Provider: <EMAIL> / Newsig1!!!

### Current Status
✅ Supabase CLI installed and configured
✅ Docker installed and running
✅ Local Supabase services running
✅ Remote schema fully imported to local database
✅ Remote data fully imported to local database
✅ **Local development isolation CONFIRMED**

## Testing Local Development Isolation ✅

Successfully tested local development isolation:

1. **Test Case**: Added new maintenance item through UI
   - Item ID: f5c8b24e-2f64-4f6e-bb6b-4e8eb6ce47ac
   - Title: "Local Test Item - Should NOT sync to production"
   - Description: "Testing local dev isolation"

2. **Verification**: Confirmed item exists only in local database
   - Local query returned the test item
   - Item should NOT appear in production database
   - Confirms complete isolation of local development environment

3. **Result**: ✅ LOCAL DEVELOPMENT IS FULLY ISOLATED FROM PRODUCTION

## Supabase Edge Functions Isolation ✅

**CONFIRMED: Edge Functions are also isolated locally!**

1. **Local Functions Server**: 
   - Running on `http://127.0.0.1:54321/functions/v1/`
   - Requires `sudo supabase functions serve` due to Docker permissions
   - Only serves functions that exist in `supabase/functions/` directory

2. **Current Local Functions**:
   - `ai-command-processor` (locally available at `/supabase/functions/ai-command-processor/`)
   - All other functions (37 total) exist only on remote production

3. **Function Routing**:
   - `supabase.functions.invoke()` automatically routes to local functions when using local Supabase
   - Local function confirmed responding with local code (tested via curl)
   - Complete isolation from production Edge Functions

4. **Development Safety**: 
   - Local function changes won't affect production
   - Only functions in local `/supabase/functions/` directory are served locally
   - Production functions remain untouched during local development

## Edge Functions Download Issue ❌

**Problem**: Attempted to download all 38 Edge Functions from remote but failed due to bundle format issues:
- `supabase functions download` failing with "InvalidV2" errors in eszip bundling
- Both regular and `--legacy-bundle` approaches failed
- All 37 remote functions failed to download

**Current Status**: 
- Only 1 function locally: `ai-command-processor`
- 37 functions remain on remote only
- App calls to missing functions will fail when using local development

**Workaround Options**:
1. **Selective Development**: Only work on features that use `ai-command-processor` 
2. **Remote Function Calls**: Configure app to call remote functions for missing ones
3. **Manual Function Recreation**: Recreate critical functions locally as needed
4. **Hybrid Approach**: Use local for new/modified functions, remote for unchanged ones

**Impact**: Limited local development capability until function sync is resolved
