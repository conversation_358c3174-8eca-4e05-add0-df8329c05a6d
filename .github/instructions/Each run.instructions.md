---
applyTo: '**'
---
We have full Api Access to Git and Supabase via cmd line API! Always USE MCP's to help you. 

You can use the logins for poperty manager role: <EMAIL> Pass: Newsig1!!! and service provider role: <EMAIL> Pass: Newsig1!!!

IMPORTANT: ALWAYS check current file list and structure in the src folder, schema, db structure, rls policies, and supabase functions, and code, before creating a new solution 
(using find & grep) that may already be built but needs to just be fixed.

Create and update any changes to the filebase sructure. Write a single line comment for what each folder and file does. Only update the one line description when you need to make changes, any pitfalls, etc. 

Make a memory.md file and use the to remember important things you learn, schema, command, Access to MCP's etc. 

Don't stop working, or ask for feedback unless you absolutely need to. 
You are running on a test server and can output unlimited tokens

Status: Downloaded newer image for public.ecr.aws/supabase/studio:2025.07.21-sha-88dca02
Started supabase local development setup.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local