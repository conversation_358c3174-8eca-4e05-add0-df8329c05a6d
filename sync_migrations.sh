#!/bin/bash

# Create empty migration files for each remote migration
# This allows us to sync the migration history

# All remaining migrations that need to be created
remaining_migrations=(
    "20240501000004" "20240501000005" "20240501000006" "20240520000000" "20240520000001"
    "20240601000000" "20240701000000" "20240701000001" "20250413172013" "20250413172211"
    "20250414170837" "20250415000000" "20250415000001" "20250415000002" "20250415000003"
    "20250415000004" "20250415000005" "20250415000006" "20250415000007" "20250415000008"
    "20250415000009" "20250415000010" "20250415000011" "20250415000012" "20250415000013"
    "20250416000001" "20250416000002" "20250416000003" "20250416000004" "20250417"
)

echo "Creating batch 2 of migration files (30 migrations)..."
for migration in "${remaining_migrations[@]}"; do
    echo "-- Migration ${migration} (already applied)" > "supabase/migrations/${migration}_applied.sql"
    echo "Created: ${migration}_applied.sql"
done

echo "Batch 2 completed. Total: ${#remaining_migrations[@]} migrations"
echo "Run this script again to process more batches if needed."
