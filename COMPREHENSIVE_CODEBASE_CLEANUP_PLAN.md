# StayFuse Comprehensive Codebase Cleanup & Production Readiness Plan

## Executive Summary

This comprehensive plan addresses the critical need to transform the StayFuse codebase from its current bloated, disorganized state into a maintainable, production-ready application. The analysis reveals **99 documentation files**, **444 TypeScript/React files**, and significant architectural inconsistencies that require systematic cleanup and modernization.

## Table of Contents

1. [Current State Analysis](#current-state-analysis)
2. [Database Architecture Analysis](#database-architecture-analysis)
3. [Team Management & Multi-tenancy](#team-management--multi-tenancy)
4. [File Structure Analysis](#file-structure-analysis)
5. [Component Architecture Issues](#component-architecture-issues)
6. [Supabase Functions & Backend](#supabase-functions--backend)
7. [Phase 1: Foundation Cleanup](#phase-1-foundation-cleanup)
8. [Phase 2: Database & Schema Optimization](#phase-2-database--schema-optimization)
9. [Phase 3: Component System Modernization](#phase-3-component-system-modernization)
10. [Phase 4: Team Management Restructure](#phase-4-team-management-restructure)
11. [Phase 5: Backend Integration Cleanup](#phase-5-backend-integration-cleanup)
12. [Phase 6: Testing & Quality Assurance](#phase-6-testing--quality-assurance)
13. [Phase 7: Performance & Production Optimization](#phase-7-performance--production-optimization)
14. [Implementation Timeline](#implementation-timeline)
15. [Risk Assessment & Mitigation](#risk-assessment--mitigation)
16. [Success Metrics & KPIs](#success-metrics--kpis)

---

## Current State Analysis

### Critical Issues Identified

#### 1. Documentation Bloat
- **99 documentation files** scattered across multiple directories
- Duplicate documentation covering same topics
- Outdated references to deleted functionality
- Inconsistent documentation formats and styles
- Critical information buried in verbose files

#### 2. File Organization Chaos
- **444 TypeScript/React files** with no clear structure
- Deep nested directories with inconsistent patterns
- Components scattered across domains without clear boundaries
- Multiple configuration files in root directory
- Test files mixed with source code

#### 3. Component Architecture Problems
- Multiple implementations of same UI patterns (buttons, forms, modals)
- No unified design system or component library
- Cross-domain dependencies creating tight coupling
- Inconsistent naming conventions
- Missing component documentation

#### 4. Backend Integration Issues
- **66 database tables** with complex relationships
- Many Supabase edge functions deleted but still referenced
- Inconsistent API patterns and error handling
- Mixed authentication strategies
- Complex multi-tenancy implementation

### Project Scale Assessment

```
Total Files Analysis:
├── TypeScript/React Files: 444
├── Documentation Files: 99
├── Test Files: 25+
├── Configuration Files: 15+
├── Database Tables: 66
├── Supabase Functions: 2 active (many deleted)
└── Dependencies: 95 production + 32 dev
```

---

## Database Architecture Analysis

### Current Schema Overview

The application uses **66 database tables** organized around core business entities:

#### Core Entity Tables
1. **User Management**
   - `profiles` - User profile information
   - `extension_api_tokens` - API tokens for Chrome extension
   
2. **Team & Multi-tenancy**
   - `team_invitations` - Team invitation management
   - `team_members` - Team membership relationships
   - `teams` - Team/organization data
   - `invitations` - General invitation system

3. **Property Management**
   - `properties` - Core property data
   - `bookings` - Property booking information
   - `collections` - Property grouping system

4. **Inventory System**
   - `inventory_items` - Physical inventory tracking  
   - `purchase_orders` - Purchase order management
   - `purchase_order_items` - Line items for orders

5. **Maintenance Operations**
   - `maintenance_tasks` - Task tracking
   - `maintenance_requests` - Service requests
   - `maintenance_providers` - Service provider directory
   - `service_providers` - External service providers

6. **Damage Management**
   - `damage_reports` - Damage incident reports
   - `damage_photos` - Photo attachments
   - `damage_notes` - Notes and updates
   - `damage_invoices` - Invoice management
   - `invoice_items` - Invoice line items

7. **System Management**
   - `backups` - Database backup tracking
   - `task_automation_rules` - Automation configuration

### Schema Issues Identified

#### 1. Normalization Problems
- Duplicate data across related tables
- Missing foreign key constraints in some relationships  
- Inconsistent column naming conventions
- Optional fields that should be required

#### 2. Multi-tenancy Complexity
- Row Level Security (RLS) policies scattered across tables
- Complex team membership resolution
- Inconsistent tenant isolation patterns
- Performance bottlenecks in team-filtered queries

#### 3. Missing Relationships
- Some tables lack proper foreign key relationships
- Cascade delete rules not properly defined
- Missing indexes for common query patterns

### Recommended Schema Optimizations

```sql
-- Example of improved table structure
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan_type TEXT DEFAULT 'free',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Improved team membership with clearer roles
CREATE TABLE team_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role team_role DEFAULT 'member',
  permissions JSONB DEFAULT '{}',
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);
```

---

## Team Management & Multi-tenancy

### Current Implementation Analysis

The application implements a complex multi-tenant architecture with the following components:

#### Team Structure
```typescript
interface Team {
  id: string;
  name: string;
  created_at: string;
  settings: TeamSettings;
  plan_type: 'free' | 'pro' | 'enterprise';
}

interface TeamMember {
  user_id: string;
  team_id: string;
  role: UserRole;
  permissions: Permission[];
  joined_at: string;
}
```

#### Role Hierarchy
1. **Admin** - Full system access
2. **Property Manager** - Team management and property operations
3. **Staff** - Limited property and maintenance access
4. **Service Provider** - Cross-team maintenance access

#### Current Problems

1. **Complex Permission Logic**
   - Permission checks scattered throughout components
   - Inconsistent role validation
   - No centralized permission management
   - Complex RLS policies causing performance issues

2. **Team Context Issues**
   - Team switching not properly implemented
   - Team context lost during navigation
   - Inconsistent team filtering in queries

3. **Invitation System Complexity**
   - Multiple invitation types and flows
   - Broken invitation acceptance process
   - No proper invitation expiration handling

### Recommended Team Management Restructure

#### 1. Simplified Role Model
```typescript
enum UserRole {
  SUPER_ADMIN = 'super_admin',
  TEAM_ADMIN = 'team_admin', 
  PROPERTY_MANAGER = 'property_manager',
  STAFF = 'staff',
  SERVICE_PROVIDER = 'service_provider'
}

interface Permission {
  resource: string;
  actions: ('create' | 'read' | 'update' | 'delete')[];
  conditions?: Record<string, any>;
}
```

#### 2. Centralized Permission System
```typescript
class PermissionManager {
  hasPermission(user: User, resource: string, action: string): boolean;
  getResourceAccess(user: User, resource: string): Permission[];
  validateTeamAccess(user: User, teamId: string): boolean;
}
```

#### 3. Improved Team Context
```typescript
interface TeamContext {
  currentTeam: Team;
  userRole: UserRole;
  permissions: Permission[];
  switchTeam: (teamId: string) => Promise<void>;
  canAccess: (resource: string, action: string) => boolean;
}
```

---

## File Structure Analysis

### Current Structure Problems

The current file structure lacks organization and consistency:

```
src/
├── components/ (scattered, 20+ subdirectories)
│   ├── admin/ (8 files + 4 subdirs)
│   ├── auth/ (7 files + 4 subdirs) 
│   ├── inventory/ (20+ files, no organization)
│   ├── properties/ (25+ files + subdirs)
│   └── [15+ other feature directories]
├── pages/ (35+ page components)
├── hooks/ (30+ custom hooks)
├── contexts/ (6 context files)
├── utils/ (15+ utility files)
├── types/ (scattered type definitions)
└── docs/ (20+ documentation files in src)
```

### Issues with Current Structure

1. **Deep Nesting**: Some directories are 4-5 levels deep
2. **Inconsistent Patterns**: Each feature area uses different organization
3. **Cross-dependencies**: Components depend on components from other features
4. **Duplicated Code**: Similar components in different feature directories
5. **No Clear Boundaries**: Business logic mixed with UI components

### Recommended File Structure

```
src/
├── app/                        # Application shell
│   ├── layout/                 # Layout components
│   ├── providers/              # Context providers
│   └── router/                 # Routing configuration
├── shared/                     # Shared/common code
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # Design system components
│   │   │   ├── Button/
│   │   │   ├── Form/
│   │   │   ├── Modal/
│   │   │   └── Layout/
│   │   └── business/           # Business logic components
│   │       ├── DataTable/
│   │       ├── ImageUpload/
│   │       └── StatusIndicator/
│   ├── hooks/                  # Reusable hooks
│   ├── utils/                  # Utility functions
│   ├── types/                  # TypeScript definitions
│   └── constants/              # Application constants
├── features/                   # Feature modules
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── index.ts
│   ├── properties/
│   │   ├── components/
│   │   │   ├── PropertyCard/
│   │   │   ├── PropertyForm/
│   │   │   └── PropertyList/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── types/
│   ├── inventory/
│   ├── maintenance/
│   ├── teams/
│   └── dashboard/
├── lib/                        # External library configurations
│   ├── supabase/
│   ├── react-query/
│   └── validation/
└── assets/                     # Static assets
    ├── icons/
    ├── images/
    └── styles/
```

---

## Component Architecture Issues

### Current Component Problems

#### 1. UI Inconsistency
- **Multiple Button Implementations**: At least 5 different button components
- **Form Variations**: Different form handling patterns across features
- **Modal Inconsistencies**: Various modal implementations with different APIs
- **Loading States**: Inconsistent loading indicators and states

#### 2. Code Duplication
Analysis reveals significant duplication:
- Image upload functionality duplicated 6+ times
- Table components with similar functionality but different implementations
- Form validation logic repeated across components
- API error handling duplicated in every component

#### 3. Component Coupling
- Components directly importing from other feature modules
- Shared state managed inconsistently
- Props drilling instead of proper context usage
- Hard-coded dependencies on specific data structures

### Component Standardization Plan

#### 1. Design System Foundation

```typescript
// Base component interfaces
interface BaseComponentProps {
  className?: string;
  testId?: string;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}

interface ButtonProps extends BaseComponentProps {
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

// Unified button component
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  ...props
}) => {
  // Single button implementation for entire app
};
```

#### 2. Universal Components

**ImageUpload Component**
```typescript
interface ImageUploadProps {
  accept?: string[];
  multiple?: boolean;
  maxSize?: number;
  onUpload: (files: File[]) => Promise<string[]>;
  onError?: (error: Error) => void;
  variant?: 'avatar' | 'gallery' | 'document';
}

const ImageUpload: React.FC<ImageUploadProps> = (props) => {
  // Single image upload implementation
  // Handles HEIC conversion, validation, progress
  // Used by all features requiring image upload
};
```

**DataTable Component**
```typescript
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  pagination?: PaginationConfig;
  filters?: FilterConfig;
  onRowClick?: (row: T) => void;
  actions?: TableAction<T>[];
}

const DataTable = <T,>(props: DataTableProps<T>) => {
  // Universal table component with sorting, filtering, pagination
  // Replaces all current table implementations
};
```

#### 3. Form System Standardization

```typescript
interface FormConfig {
  schema: ZodSchema;
  defaultValues: Record<string, any>;
  onSubmit: (values: any) => Promise<void>;
}

const FormProvider: React.FC<FormConfig> = ({
  schema,
  defaultValues,
  onSubmit,
  children
}) => {
  // Unified form handling with validation
  // Consistent error handling and loading states
};

// Form components
const FormField: React.FC<FormFieldProps> = (props) => {
  // Standardized form field with validation display
};

const FormActions: React.FC<FormActionsProps> = (props) => {
  // Consistent form action buttons (Save, Cancel, etc.)
};
```

---

## Supabase Functions & Backend

### Current Backend State

#### Active Functions
Only **2 Supabase Edge Functions** remain active:
1. `ai-command-processor/index.ts` - AI command processing
2. `ai-command-processor/intelligentSuggestions.ts` - AI suggestions

#### Deleted Functions (Still Referenced)
The codebase contains references to **40+ deleted functions**:
- `accept-invitation-direct`
- `admin-*` functions (create-user, delete-user, etc.)
- `maintenance-response`
- `upload-*` functions (damage-photo, invoice-pdf, etc.)
- `sync-property-calendars`
- And many others

#### Issues with Current Backend

1. **Dead Code References**
   - Import statements for deleted functions
   - API calls to non-existent endpoints  
   - Type definitions for removed functionality
   - Error handling for deleted services

2. **Inconsistent API Patterns**
   - Mixed use of direct Supabase client calls and function calls
   - Inconsistent error handling across API calls
   - No standardized request/response patterns
   - Authentication inconsistencies

3. **Missing API Layer**
   - No centralized API service
   - Business logic scattered in components
   - No request/response transformation layer
   - No consistent caching strategy

### Backend Restructure Plan

#### 1. API Service Layer

```typescript
// Centralized API service
class ApiService {
  private supabase: SupabaseClient;
  
  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  // Standardized CRUD operations
  async create<T>(table: string, data: Partial<T>): Promise<T>;
  async read<T>(table: string, id: string): Promise<T>;
  async update<T>(table: string, id: string, data: Partial<T>): Promise<T>;
  async delete(table: string, id: string): Promise<void>;
  
  // Query builder interface
  query<T>(table: string): QueryBuilder<T>;
}

// Feature-specific API services
class PropertyService extends ApiService {
  async getProperties(teamId: string): Promise<Property[]>;
  async createProperty(property: CreatePropertyDto): Promise<Property>;
  async updateProperty(id: string, updates: UpdatePropertyDto): Promise<Property>;
}
```

#### 2. Error Handling Standardization

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

class ApiErrorHandler {
  static handle(error: any): ApiError {
    // Standardized error transformation
    // Consistent error logging
    // User-friendly error messages
  }
  
  static isRetryable(error: ApiError): boolean {
    // Determine if error should trigger retry
  }
}
```

#### 3. Request/Response Transformation

```typescript
interface ApiRequest<T = any> {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  endpoint: string;
  data?: T;
  params?: Record<string, string>;
  headers?: Record<string, string>;
}

interface ApiResponse<T = any> {
  data: T;
  meta?: {
    pagination?: PaginationMeta;
    total?: number;
  };
}

class RequestTransformer {
  static transformRequest<T>(request: ApiRequest<T>): SupabaseQuery;
  static transformResponse<T>(response: any): ApiResponse<T>;
}
```

---

## Phase 0: Testing Foundation & Safety Infrastructure

### Priority: CRITICAL - MUST COMPLETE BEFORE ANY CHANGES
**Timeline: Week 1**

#### 0.1 Current Testing State Analysis

**CRITICAL ISSUES**: Multiple system-breaking problems must be fixed BEFORE any cleanup begins.

**Testing Infrastructure:**
- Only **1 working test** (`src/working.test.js` with basic functionality)
- **25+ test files exist** but most are broken/incomplete  
- Multiple test frameworks configured but not properly integrated
- No API mocking, integration tests, or proper test utilities

**🚨 EDGE FUNCTIONS CRISIS:**
- App calls **25 different Edge Functions** in 37+ files
- Only **1 function exists locally** (`ai-command-processor`)
- **24 missing functions** will cause app failures:
  - `send-email`, `admin-backup-database`, `create-team-invitation`
  - `upload-inventory-image`, `sync-property-calendars`, etc.
- Local development severely limited without function access

#### 0.2 Establish Testing Baseline (BEFORE any changes)

**PERFECT SETUP**: We have isolated local Supabase with real production data copy:
- ✅ **9,675+ auth records** imported locally
- ✅ **18 profiles** with real business data  
- ✅ **All 66 database tables** with production schema
- ✅ **CONFIRMED ISOLATION**: Local changes DON'T sync to production
- ✅ **Test verified**: Added maintenance task locally, stayed local only

**This is the IDEAL cleanup environment - real data with zero production risk!**

**Phase 0A: Edge Functions Crisis Resolution (Day 1-2)**
```bash
# 1. FIRST: Document Edge Function usage crisis
./scripts/audit-edge-functions.sh  # Creates list of all missing functions

# 2. Choose Edge Function strategy (CRITICAL DECISION):
# OPTION A: Hybrid Mode (Recommended)
#   - Keep using remote functions for missing ones
#   - Use local only for ai-command-processor
echo "VITE_USE_REMOTE_FUNCTIONS=true" >> .env.local

# OPTION B: Mock Missing Functions (For testing only)
#   - Create mock implementations for testing
#   - Risk: May hide real issues

# OPTION C: Recreate Critical Functions (Time-intensive)
#   - Manually recreate essential functions locally
#   - Only do for absolutely critical functions

# 3. Test chosen strategy
npm run build
VITE_SUPABASE_URL=http://127.0.0.1:54321 npm run dev
# Verify app doesn't crash on function calls
```

**Phase 0B: Environment Setup (Day 2-3)**
```bash
# 1. Verify environment is ready
node --version  # Should be v18+
npm --version   # Should be v9+
supabase --version  # Should be v2.31.8+ (✅ confirmed)

# 2. Verify local Supabase is running with real data
supabase status  # Should show all services HEALTHY
supabase db dump --local --schema public -f local_schema_verify.sql

# 3. Install missing testing dependencies  
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event
npm install --save-dev vitest @vitest/ui jsdom

# 4. Create complete backup of current working state
git add -A && git commit -m "BASELINE: Working state with Edge Functions strategy - $(date)"
git tag -a "baseline-v1.0" -m "Tested working baseline with function handling"

# 5. Document ALL current functionality
./scripts/audit-current-functionality.sh
```

**Critical Verification Checklist (with Edge Functions Solution):**
- [ ] Local Supabase services running on ports 54321-54324
- [ ] **Edge Functions Crisis RESOLVED** - Hybrid handler implemented
- [ ] Can <NAME_EMAIL> / Newsig1!!! (LOCAL auth)
- [ ] Dashboard loads without function errors (hybrid functions working)
- [ ] Properties page functional with hybrid function calls
- [ ] Inventory management works (remote functions + local data)
- [ ] Maintenance system operational (admin functions via remote)
- [ ] Team management functional (invitation functions via remote)
- [ ] Email sending works (send-email function via remote)
- [ ] Admin panel functional (admin-* functions via remote)
- [ ] **CONFIRM**: No function call failures in console
- [ ] **CONFIRM**: Local data changes stay local (isolation preserved)

**Phase 0B: Testing Infrastructure (Days 2-3)**
```bash
# 1. Configure tests to use LOCAL Supabase (no mocking needed!)
# Update test environment to point to local Supabase
echo "VITE_SUPABASE_URL=http://127.0.0.1:54321" > .env.test
echo "VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" >> .env.test

# 2. Create realistic snapshots using REAL local data
npm run test:baseline-snapshots

# 3. NO MOCKING NEEDED - use real local Supabase database
# Tests will run against local PostgreSQL with real schema and data

# 4. Create integration tests using local database
npm run test:integration-with-local-supabase
```

**Phase 0C: Regression Detection (Days 4-5)**
```bash
# 1. Create automated functionality verification
./scripts/verify-critical-workflows.sh

# 2. Set up visual regression testing with current UI
npx playwright test --grep="visual-baseline"

# 3. Database integrity checks
./scripts/verify-database-operations.sh
```

#### 0.3 Critical Functionality Snapshot Tests

```typescript
// tests/snapshots/critical-functionality.test.tsx
describe('Critical Functionality Snapshots - BASELINE', () => {
  it('Dashboard renders without crashing', () => {
    const { container } = render(<Dashboard />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('Properties page renders without crashing', () => {
    const { container } = render(<Properties />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('Inventory page renders without crashing', () => {
    const { container } = render(<Inventory />);
    expect(container.firstChild).toMatchSnapshot();
  });
  
  // Snapshot ALL major components BEFORE changes
});
```

#### 0.4 Automated Regression Detection

```bash
# scripts/verify-no-regression.sh
#!/bin/bash
set -e

echo "🔍 Running regression checks..."

# 1. Build check
npm run build

# 2. Snapshot verification
npm run test:snapshots -- --updateSnapshot=false

# 3. Critical functionality tests
npm run test:critical

# 4. Performance baseline
npm run test:performance-baseline

echo "✅ No regressions detected"
```

#### 0.5 Safety Mechanisms

**Feature Flags for Gradual Rollout:**
```typescript
// Enable new components gradually
export const featureFlags = {
  useNewButton: process.env.REACT_APP_USE_NEW_BUTTON === 'true',
  useNewFormSystem: process.env.REACT_APP_USE_NEW_FORMS === 'true',
};

const Button = featureFlags.useNewButton ? NewButton : OldButton;
```

**Automated Rollback Procedures:**
```bash
# If any test fails, automatically revert
git revert --no-commit HEAD~$1 && npm run test:critical
```

### Phase 0 Deliverables (MUST COMPLETE FIRST)

**Day 1 - Immediate Safety:**
- [ ] Git baseline tag created with working state
- [ ] Current functionality audit completed
- [ ] Real user login verified (<EMAIL>)
- [ ] Build process confirmed working

**Days 2-3 - Testing Infrastructure:**  
- [ ] Fixed Jest configuration running all tests
- [ ] Complete snapshots using real production data
- [ ] Supabase mocking matching actual schema (66 tables)
- [ ] Integration tests with real database structure

**Days 4-5 - Regression Detection:**
- [ ] Automated functionality verification scripts
- [ ] Visual regression baselines captured
- [ ] Database operation integrity checks
- [ ] Feature flag system for safe rollouts
- [ ] Rollback procedures and safety checks

**GATE CHECK**: All functionality must work identically after Phase 0
- [ ] Login with real credentials still works
- [ ] Dashboard loads with real data
- [ ] Property/inventory/maintenance pages functional
- [ ] Database operations complete successfully
- [ ] No console errors or broken functionality

**🚨 CRITICAL: No Phase 1 work begins until Phase 0 is 100% complete**

---

## Phase 1: Foundation Cleanup

### Priority: HIGH  
**Timeline: Week 2-3**

#### 1.1 Root Directory Organization

**SAFETY FIRST**: Each change must pass regression tests

**Current Issues:**
- 15+ configuration files in root
- Screenshots and test files scattered
- Multiple documentation files
- Development artifacts

**Actions (with testing after each step):**
```bash
# 1. Move configuration files (SAFE - no code changes)
mkdir -p config
mv *.config.* config/
./scripts/verify-no-regression.sh

# 2. Consolidate documentation (SAFE - no code changes)  
mkdir -p docs
mv *.md docs/
./scripts/verify-no-regression.sh

# 3. Clean up artifacts (SAFE - removing unused files)
rm -rf *-screenshots/
rm test-*.js manual-test.js
./scripts/verify-no-regression.sh

# 4. Final verification
npm run test:snapshots -- --updateSnapshot=false
npm run build
npm run dev # Verify app still starts
```

**Testing Requirements:**
- [ ] Snapshots must match after each change
- [ ] Build must succeed after each change  
- [ ] Dev server must start after each change
- [ ] No functionality regression detected

**Expected Outcome:**
- Clean root directory with only essential files
- All documentation in single location  
- Configuration files organized
- Development artifacts removed
- **ZERO functionality changes**

#### 1.2 Documentation Consolidation

**Current State: 99 documentation files**
**Target: 10-15 well-organized documents**

**Consolidation Strategy:**

1. **Merge Similar Documents**
   ```
   DataLoading*.md (8 files) → docs/architecture/data-loading.md
   AI*.md (12 files) → docs/features/ai-system.md
   Auth*.md (6 files) → docs/architecture/authentication.md
   ```

2. **Create Master Documents**
   - `docs/README.md` - Project overview and quick start
   - `docs/DEVELOPMENT.md` - Development setup and guidelines
   - `docs/ARCHITECTURE.md` - System architecture overview
   - `docs/API.md` - API documentation and patterns
   - `docs/DEPLOYMENT.md` - Deployment and production guide

3. **Remove Obsolete Documentation**
   - Delete docs for removed features
   - Remove duplicate troubleshooting guides
   - Archive outdated implementation notes

**Documentation Structure:**
```
docs/
├── README.md                   # Main project documentation
├── DEVELOPMENT.md             # Development setup
├── ARCHITECTURE.md            # System overview
├── API.md                     # API documentation
├── DEPLOYMENT.md              # Production deployment
├── architecture/              # Detailed architecture docs
│   ├── database.md
│   ├── authentication.md
│   └── data-loading.md
├── features/                  # Feature-specific docs
│   ├── team-management.md
│   ├── property-management.md
│   └── ai-system.md
└── troubleshooting/           # Common issues
    ├── known-issues.md
    └── development-setup.md
```

#### 1.3 Test Organization

**Current Issues:**
- Tests scattered across directories
- Multiple test frameworks (Jest, Cypress, Playwright)
- Duplicate test configurations
- Test files mixed with source code

**Actions:**

1. **Standardize Test Framework**
   ```bash
   # Remove duplicate test configs
   rm cypress.config.ts jest.config.ts playwright.config.ts
   
   # Create single test configuration
   mv jest.config.js config/jest.config.js
   ```

2. **Consolidate Test Files**
   ```bash
   # Move all tests to tests directory
   mkdir -p tests/{unit,integration,e2e}
   mv src/**/*.test.* tests/unit/
   mv cypress/e2e/* tests/e2e/
   mv tests/*.test.* tests/integration/
   ```

3. **Create Test Utilities**
   ```typescript
   // tests/utils/test-utils.tsx
   export const renderWithProviders = (ui: React.ReactElement) => {
     // Common test setup with providers
   };
   
   export const mockSupabaseClient = () => {
     // Standardized Supabase mocking
   };
   ```

**Expected Test Structure:**
```
tests/
├── config/                    # Test configurations
├── utils/                     # Test utilities and helpers
├── mocks/                     # Mock data and services
├── unit/                      # Unit tests
│   ├── components/
│   ├── hooks/
│   └── utils/
├── integration/               # Integration tests
│   ├── api/
│   └── features/
└── e2e/                      # End-to-end tests
    ├── auth/
    ├── properties/
    └── team-management/
```

### Phase 1 Deliverables

- [ ] Clean root directory structure
- [ ] Consolidated documentation (99 → 15 files)
- [ ] Organized test structure
- [ ] Removed obsolete files and artifacts
- [ ] Updated build scripts and configurations

---

## Phase 2: Database & Schema Optimization

### Priority: HIGH
**Timeline: Week 3-4**

#### 2.1 Database Safety with Local Supabase (SAFE APPROACH)

**✅ PERFECT SETUP**: We're using isolated local Supabase with production data copy!
- **Local database**: All changes stay local (127.0.0.1:54322)
- **Real data**: 9,675+ auth records, 18 profiles, 66 tables
- **Zero production risk**: Confirmed isolation testing completed
- **Full rollback**: Can reset local DB anytime with `supabase db reset`

**Safe Database Change Protocol:**
```bash
# BEFORE any schema changes to local database
# 1. Create snapshot of current local state
supabase db dump --local --data-only -f "local_backup_$(date +%Y%m%d_%H%M%S).sql"

# 2. Test schema changes on LOCAL database (safe!)
# Make changes to local Supabase - no production risk

# 3. If changes work, document them for later production deployment
./scripts/document-schema-changes.sh

# 4. If changes break something, easily rollback
supabase db reset  # Resets to original state
# OR restore from backup:
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f local_backup_*.sql

# 5. Only create production migrations after local testing succeeds
```

**Safety Rules (Updated for Local Development):**
- ✅ **Safe to experiment** - local database is isolated
- ✅ **Real data testing** - use production data copy locally  
- ✅ **Easy rollback** - `supabase db reset` restores original state
- ✅ **Zero production risk** - changes never sync to production
- ❌ **Never run untested changes on production** - test locally first

#### 2.2 Schema Analysis & Optimization

**Current Schema Issues:**
- Inconsistent naming conventions
- Missing foreign key constraints
- Complex RLS policies causing performance issues
- Denormalized data structures

**SAFETY-FIRST Actions:**

1. **Schema Audit**
   ```sql
   -- Analyze current schema
   SELECT 
     schemaname,
     tablename,
     tableowner,
     tablespace
   FROM pg_tables 
   WHERE schemaname = 'public';
   
   -- Check foreign key constraints
   SELECT 
     tc.table_name,
     tc.constraint_name,
     kcu.column_name,
     ccu.table_name AS foreign_table_name,
     ccu.column_name AS foreign_column_name
   FROM information_schema.table_constraints AS tc
   JOIN information_schema.key_column_usage AS kcu
     ON tc.constraint_name = kcu.constraint_name
   JOIN information_schema.constraint_column_usage AS ccu
     ON ccu.constraint_name = tc.constraint_name
   WHERE tc.constraint_type = 'FOREIGN KEY';
   ```

2. **Normalize Table Structures**
   ```sql
   -- Example: Improve inventory_items table
   CREATE TABLE inventory_items_v2 (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     name TEXT NOT NULL,
     collection_id UUID REFERENCES collections(id) ON DELETE SET NULL,
     property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     quantity INTEGER NOT NULL DEFAULT 0,
     min_quantity INTEGER NOT NULL DEFAULT 0,
     price DECIMAL(10,2),
     metadata JSONB DEFAULT '{}',
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW(),
     
     -- Constraints
     CONSTRAINT positive_quantity CHECK (quantity >= 0),
     CONSTRAINT positive_min_quantity CHECK (min_quantity >= 0)
   );
   
   -- Add indexes for common queries
   CREATE INDEX idx_inventory_property_collection 
     ON inventory_items_v2(property_id, collection_id);
   CREATE INDEX idx_inventory_low_stock 
     ON inventory_items_v2(property_id) WHERE quantity <= min_quantity;
   ```

3. **Optimize RLS Policies**
   ```sql
   -- Simplified team-based RLS
   CREATE POLICY "Users can access team inventory" ON inventory_items_v2
     FOR ALL USING (
       property_id IN (
         SELECT p.id FROM properties p
         JOIN team_properties tp ON tp.property_id = p.id
         JOIN team_members tm ON tm.team_id = tp.team_id
         WHERE tm.user_id = auth.uid()
       )
     );
   ```

#### 2.2 Data Migration Strategy

**Migration Approach:**
1. Create new optimized tables alongside existing ones
2. Migrate data in batches during low-traffic periods
3. Update application queries to use new tables
4. Remove old tables after validation

**Migration Script Example:**
```sql
-- migration_001_optimize_inventory.sql
BEGIN;

-- Create optimized table
CREATE TABLE inventory_items_new (
  -- Improved schema definition
);

-- Migrate data
INSERT INTO inventory_items_new (
  id, name, collection_id, property_id, user_id, 
  quantity, min_quantity, price, created_at, updated_at
)
SELECT 
  id, name, collection_id, property_id, user_id,
  quantity, min_quantity, price, created_at, updated_at
FROM inventory_items;

-- Rename tables
ALTER TABLE inventory_items RENAME TO inventory_items_old;
ALTER TABLE inventory_items_new RENAME TO inventory_items;

COMMIT;
```

#### 2.3 Performance Optimization

**Query Optimization:**
```sql
-- Add strategic indexes
CREATE INDEX CONCURRENTLY idx_maintenance_tasks_property_status 
  ON maintenance_tasks(property_id, status) 
  WHERE status != 'completed';

CREATE INDEX CONCURRENTLY idx_damage_reports_property_date 
  ON damage_reports(property_id, created_at DESC);

-- Optimize team membership queries
CREATE INDEX CONCURRENTLY idx_team_members_user_lookup 
  ON team_members(user_id, team_id, role);
```

**Connection Pooling:**
```typescript
// lib/supabase/client.ts
const supabaseConfig = {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: { 'x-my-custom-header': 'stayfuse' },
  },
  // Connection pooling for production
  ...(process.env.NODE_ENV === 'production' && {
    db: {
      pool: {
        min: 2,
        max: 10,
      }
    }
  })
};
```

### Phase 2 Deliverables

- [ ] Optimized database schema with proper constraints
- [ ] Improved RLS policies for better performance
- [ ] Strategic indexes for common query patterns
- [ ] Data migration scripts and rollback procedures
- [ ] Performance benchmarks and monitoring

---

## Phase 3: Component System Modernization

### Priority: HIGH
**Timeline: Week 4-6**

#### 3.1 Design System Foundation

**Current Component Problems:**
- 5+ different button implementations
- Inconsistent form handling patterns
- Multiple modal implementations
- No unified styling approach

**Solution: Create Comprehensive Design System**

```typescript
// shared/components/ui/Button/Button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary', 
    size = 'md', 
    loading = false, 
    icon, 
    fullWidth = false,
    children, 
    className,
    disabled,
    ...props 
  }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(
          buttonVariants({ variant, size, fullWidth }),
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <Spinner size="sm" />}
        {icon && !loading && icon}
        {children}
      </button>
    );
  }
);

// Variants using class-variance-authority
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
        secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",
        ghost: "text-gray-600 hover:bg-gray-100 focus:ring-gray-500",
        destructive: "bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"
      },
      size: {
        sm: "h-8 px-3 text-sm",
        md: "h-10 px-4 text-sm",
        lg: "h-12 px-6 text-base"
      },
      fullWidth: {
        true: "w-full"
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "md"
    }
  }
);
```

#### 3.2 Universal Form System

```typescript
// shared/components/ui/Form/FormProvider.tsx
interface FormProviderProps<T extends FieldValues> {
  schema: ZodSchema<T>;
  defaultValues?: DeepPartial<T>;
  onSubmit: (values: T) => Promise<void>;
  children: React.ReactNode;
}

export const FormProvider = <T extends FieldValues>({
  schema,
  defaultValues,
  onSubmit,
  children
}: FormProviderProps<T>) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues
  });

  const handleSubmit = async (values: T) => {
    try {
      await onSubmit(values);
      toast.success('Changes saved successfully');
    } catch (error) {
      toast.error('Failed to save changes');
      console.error('Form submission error:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        {children}
      </form>
    </Form>
  );
};

// shared/components/ui/Form/FormField.tsx
interface FormFieldProps {
  name: string;
  label?: string;
  description?: string;
  required?: boolean;
  children: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  description,
  required,
  children
}) => {
  const { control } = useFormContext();
  
  return (
    <FormFieldWrapper
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && (
            <FormLabel className={required ? 'required' : ''}>
              {label}
            </FormLabel>
          )}
          <FormControl>
            {React.cloneElement(children as React.ReactElement, {
              ...field,
              error: fieldState.error
            })}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
```

#### 3.3 Universal Image Upload Component

```typescript
// shared/components/business/ImageUpload/ImageUpload.tsx
interface ImageUploadProps {
  value?: string | string[];
  onChange: (urls: string | string[]) => void;
  multiple?: boolean;
  accept?: string[];
  maxSize?: number; // in MB
  bucket: string;
  folder?: string;
  variant?: 'avatar' | 'gallery' | 'document';
  className?: string;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  multiple = false,
  accept = ['image/*'],
  maxSize = 10,
  bucket,
  folder = 'uploads',
  variant = 'gallery',
  className
}) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleUpload = async (files: File[]) => {
    setUploading(true);
    setProgress(0);

    try {
      const uploadPromises = files.map(async (file, index) => {
        // Handle HEIC conversion
        const processedFile = await convertHeicIfNeeded(file);
        
        // Generate unique filename
        const fileName = `${folder}/${Date.now()}-${processedFile.name}`;
        
        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from(bucket)
          .upload(fileName, processedFile, {
            onUploadProgress: (progressEvent) => {
              const fileProgress = (progressEvent.loaded / progressEvent.total) * 100;
              const totalProgress = ((index * 100) + fileProgress) / files.length;
              setProgress(totalProgress);
            }
          });

        if (error) throw error;

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from(bucket)
          .getPublicUrl(data.path);

        return publicUrl;
      });

      const urls = await Promise.all(uploadPromises);
      
      if (multiple) {
        const currentUrls = Array.isArray(value) ? value : [];
        onChange([...currentUrls, ...urls]);
      } else {
        onChange(urls[0]);
      }

      toast.success(`${files.length} file(s) uploaded successfully`);
    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Upload failed. Please try again.');
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <div className={cn('image-upload', className)}>
      <Dropzone
        onDrop={handleUpload}
        accept={accept}
        multiple={multiple}
        maxSize={maxSize * 1024 * 1024}
        disabled={uploading}
      >
        {({ getRootProps, getInputProps, isDragActive }) => (
          <div
            {...getRootProps()}
            className={cn(
              'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
              isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300',
              uploading && 'cursor-not-allowed opacity-50'
            )}
          >
            <input {...getInputProps()} />
            
            {uploading ? (
              <div className="space-y-2">
                <Spinner size="lg" />
                <p>Uploading... {Math.round(progress)}%</p>
                <Progress value={progress} className="w-full" />
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="mx-auto h-8 w-8 text-gray-400" />
                <p className="text-sm text-gray-600">
                  {isDragActive
                    ? 'Drop files here...'
                    : 'Drag & drop files here, or click to select'
                  }
                </p>
                <p className="text-xs text-gray-400">
                  Max {maxSize}MB per file
                </p>
              </div>
            )}
          </div>
        )}
      </Dropzone>

      {/* Preview uploaded images */}
      {value && (
        <ImagePreview
          urls={Array.isArray(value) ? value : [value]}
          onRemove={(url) => {
            if (multiple && Array.isArray(value)) {
              onChange(value.filter(v => v !== url));
            } else {
              onChange('');
            }
          }}
          variant={variant}
        />
      )}
    </div>
  );
};
```

#### 3.4 Universal Data Table

```typescript
// shared/components/business/DataTable/DataTable.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    pageCount?: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (size: number) => void;
  };
  sorting?: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    onSortChange: (column: string, order: 'asc' | 'desc') => void;
  };
  filtering?: {
    globalFilter?: string;
    columnFilters?: Record<string, any>;
    onGlobalFilterChange: (filter: string) => void;
    onColumnFilterChange: (column: string, value: any) => void;
  };
  selection?: {
    selectedRows: T[];
    onSelectionChange: (rows: T[]) => void;
  };
  actions?: {
    bulk?: Array<{
      label: string;
      icon?: React.ReactNode;
      onClick: (rows: T[]) => void;
      variant?: 'default' | 'destructive';
    }>;
    row?: Array<{
      label: string;
      icon?: React.ReactNode;
      onClick: (row: T) => void;
      variant?: 'default' | 'destructive';
      show?: (row: T) => boolean;
    }>;
  };
  emptyState?: {
    icon?: React.ReactNode;
    title: string;
    description?: string;
    action?: {
      label: string;
      onClick: () => void;
    };
  };
}

export const DataTable = <T,>({
  data,
  columns,
  loading = false,
  pagination,
  sorting,
  filtering,
  selection,
  actions,
  emptyState
}: DataTableProps<T>) => {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    // ... other table configuration
  });

  if (loading) {
    return <DataTableSkeleton />;
  }

  if (data.length === 0 && emptyState) {
    return <DataTableEmptyState {...emptyState} />;
  }

  return (
    <div className="data-table">
      {/* Table Header with filters and actions */}
      <DataTableHeader
        table={table}
        filtering={filtering}
        actions={actions}
        selection={selection}
      />

      {/* Main table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && (
        <DataTablePagination
          table={table}
          pagination={pagination}
        />
      )}
    </div>
  );
};
```

### Phase 3 Deliverables

- [ ] Complete design system with all UI components
- [ ] Universal form system with validation
- [ ] Single image upload component for all features
- [ ] Reusable data table component
- [ ] Component documentation and usage examples
- [ ] Migration guide for existing components

---

## Phase 4: Team Management Restructure

### Priority: MEDIUM
**Timeline: Week 5-7**

#### 4.1 Simplified Team Architecture

**Current Issues:**
- Complex role hierarchy with unclear permissions
- Team context lost during navigation
- Broken invitation system
- Performance issues with team filtering

**New Team Architecture:**

```typescript
// shared/types/team.ts
export interface Team {
  id: string;
  name: string;
  slug: string;
  plan: 'free' | 'pro' | 'enterprise';
  settings: TeamSettings;
  created_at: string;
  updated_at: string;
}

export interface TeamMember {
  id: string;
  team_id: string;
  user_id: string;
  role: TeamRole;
  permissions: Permission[];
  joined_at: string;
  invited_by?: string;
}

export enum TeamRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  GUEST = 'guest'
}

export interface Permission {
  resource: string;
  actions: PermissionAction[];
  conditions?: Record<string, any>;
}

export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage'
}
```

#### 4.2 Centralized Permission System

```typescript
// shared/services/PermissionService.ts
export class PermissionService {
  private static rolePermissions: Record<TeamRole, Permission[]> = {
    [TeamRole.OWNER]: [
      { resource: '*', actions: [PermissionAction.MANAGE] }
    ],
    [TeamRole.ADMIN]: [
      { resource: 'team', actions: [PermissionAction.MANAGE] },
      { resource: 'properties', actions: [PermissionAction.MANAGE] },
      { resource: 'inventory', actions: [PermissionAction.MANAGE] },
      { resource: 'maintenance', actions: [PermissionAction.MANAGE] }
    ],
    [TeamRole.MANAGER]: [
      { resource: 'properties', actions: [PermissionAction.CREATE, PermissionAction.READ, PermissionAction.UPDATE] },
      { resource: 'inventory', actions: [PermissionAction.MANAGE] },
      { resource: 'maintenance', actions: [PermissionAction.MANAGE] }
    ],
    [TeamRole.MEMBER]: [
      { resource: 'properties', actions: [PermissionAction.READ] },
      { resource: 'inventory', actions: [PermissionAction.READ, PermissionAction.UPDATE] },
      { resource: 'maintenance', actions: [PermissionAction.CREATE, PermissionAction.READ, PermissionAction.UPDATE] }
    ],
    [TeamRole.GUEST]: [
      { resource: 'properties', actions: [PermissionAction.READ] },
      { resource: 'inventory', actions: [PermissionAction.READ] }
    ]
  };

  static hasPermission(
    userRole: TeamRole,
    resource: string,
    action: PermissionAction,
    conditions?: Record<string, any>
  ): boolean {
    const permissions = this.rolePermissions[userRole] || [];
    
    return permissions.some(permission => {
      const resourceMatch = permission.resource === '*' || permission.resource === resource;
      const actionMatch = permission.actions.includes(PermissionAction.MANAGE) || 
                         permission.actions.includes(action);
      
      if (!resourceMatch || !actionMatch) return false;
      
      // Check conditions if provided
      if (permission.conditions && conditions) {
        return this.checkConditions(permission.conditions, conditions);
      }
      
      return true;
    });
  }

  static getResourceActions(userRole: TeamRole, resource: string): PermissionAction[] {
    const permissions = this.rolePermissions[userRole] || [];
    const resourcePermission = permissions.find(p => 
      p.resource === '*' || p.resource === resource
    );
    
    if (!resourcePermission) return [];
    
    return resourcePermission.actions.includes(PermissionAction.MANAGE)
      ? Object.values(PermissionAction)
      : resourcePermission.actions;
  }

  private static checkConditions(
    requiredConditions: Record<string, any>,
    providedConditions: Record<string, any>
  ): boolean {
    return Object.entries(requiredConditions).every(([key, value]) => 
      providedConditions[key] === value
    );
  }
}
```

#### 4.3 Team Context Provider

```typescript
// shared/contexts/TeamContext.tsx
interface TeamContextValue {
  currentTeam: Team | null;
  userRole: TeamRole | null;
  permissions: Permission[];
  teams: Team[];
  isLoading: boolean;
  error: Error | null;
  
  // Actions
  switchTeam: (teamId: string) => Promise<void>;
  refreshTeamData: () => Promise<void>;
  
  // Permission helpers
  can: (resource: string, action: PermissionAction, conditions?: Record<string, any>) => boolean;
  canManage: (resource: string) => boolean;
  getResourceActions: (resource: string) => PermissionAction[];
}

const TeamContext = createContext<TeamContextValue | null>(null);

export const TeamProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const { user } = useAuth();
  const [currentTeam, setCurrentTeam] = useState<Team | null>(null);
  const [userRole, setUserRole] = useState<TeamRole | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Load user's teams and set current team
  useEffect(() => {
    if (!user) return;
    
    loadUserTeams();
  }, [user]);

  const loadUserTeams = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch user's team memberships
      const { data: memberships, error: membershipError } = await supabase
        .from('team_members')
        .select(`
          role,
          team:teams(*)
        `)
        .eq('user_id', user.id);

      if (membershipError) throw membershipError;

      const userTeams = memberships.map(m => m.team);
      setTeams(userTeams);

      // Set current team (from localStorage or first team)
      const savedTeamId = localStorage.getItem('currentTeamId');
      const targetTeam = savedTeamId 
        ? userTeams.find(t => t.id === savedTeamId)
        : userTeams[0];

      if (targetTeam) {
        const membership = memberships.find(m => m.team.id === targetTeam.id);
        setCurrentTeam(targetTeam);
        setUserRole(membership.role);
      }
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const switchTeam = async (teamId: string) => {
    const team = teams.find(t => t.id === teamId);
    if (!team) return;

    const { data: membership } = await supabase
      .from('team_members')
      .select('role')
      .eq('team_id', teamId)
      .eq('user_id', user.id)
      .single();

    setCurrentTeam(team);
    setUserRole(membership.role);
    localStorage.setItem('currentTeamId', teamId);
  };

  const can = (
    resource: string, 
    action: PermissionAction, 
    conditions?: Record<string, any>
  ): boolean => {
    if (!userRole) return false;
    return PermissionService.hasPermission(userRole, resource, action, conditions);
  };

  const canManage = (resource: string): boolean => {
    return can(resource, PermissionAction.MANAGE);
  };

  const getResourceActions = (resource: string): PermissionAction[] => {
    if (!userRole) return [];
    return PermissionService.getResourceActions(userRole, resource);
  };

  const value: TeamContextValue = {
    currentTeam,
    userRole,
    permissions: userRole ? PermissionService.rolePermissions[userRole] : [],
    teams,
    isLoading,
    error,
    switchTeam,
    refreshTeamData: loadUserTeams,
    can,
    canManage,
    getResourceActions
  };

  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
};

export const useTeam = () => {
  const context = useContext(TeamContext);
  if (!context) {
    throw new Error('useTeam must be used within TeamProvider');
  }
  return context;
};
```

#### 4.4 Permission-Based Components

```typescript
// shared/components/business/PermissionGate/PermissionGate.tsx
interface PermissionGateProps {
  resource: string;
  action: PermissionAction;
  conditions?: Record<string, any>;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  resource,
  action,
  conditions,
  fallback = null,
  children
}) => {
  const { can } = useTeam();

  if (!can(resource, action, conditions)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Usage examples:
const PropertyActions = () => {
  return (
    <div className="flex gap-2">
      <PermissionGate resource="properties" action={PermissionAction.UPDATE}>
        <Button variant="secondary">Edit Property</Button>
      </PermissionGate>
      
      <PermissionGate resource="properties" action={PermissionAction.DELETE}>
        <Button variant="destructive">Delete Property</Button>
      </PermissionGate>
      
      <PermissionGate resource="properties" action={PermissionAction.CREATE}>
        <Button>Add Property</Button>
      </PermissionGate>
    </div>
  );
};
```

#### 4.5 Improved Invitation System

```typescript
// features/teams/services/InvitationService.ts
export class InvitationService {
  static async sendInvitation(params: {
    teamId: string;
    email: string;
    role: TeamRole;
    invitedBy: string;
  }): Promise<{ invitationId: string }> {
    // Create invitation record
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .insert({
        team_id: params.teamId,
        email: params.email,
        role: params.role,
        invited_by: params.invitedBy,
        token: generateInvitationToken(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      })
      .select()
      .single();

    if (error) throw error;

    // Send invitation email via Supabase Edge Function
    await supabase.functions.invoke('send-team-invitation', {
      body: {
        invitationId: invitation.id,
        email: params.email,
        teamName: 'Team Name', // Get from team data
        inviterName: 'Inviter Name' // Get from user data
      }
    });

    return { invitationId: invitation.id };
  }

  static async acceptInvitation(token: string): Promise<void> {
    // Verify and accept invitation
    const { data: invitation, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .eq('status', 'pending')
      .single();

    if (error || !invitation) {
      throw new Error('Invalid or expired invitation');
    }

    if (new Date(invitation.expires_at) < new Date()) {
      throw new Error('Invitation has expired');
    }

    // Add user to team
    await supabase.from('team_members').insert({
      team_id: invitation.team_id,
      user_id: invitation.user_id,
      role: invitation.role
    });

    // Mark invitation as accepted
    await supabase
      .from('team_invitations')
      .update({ status: 'accepted', accepted_at: new Date().toISOString() })
      .eq('id', invitation.id);
  }
}
```

### Phase 4 Deliverables

- [ ] Simplified team architecture with clear roles
- [ ] Centralized permission system
- [ ] Team context provider with permission helpers
- [ ] Permission-based UI components
- [ ] Improved invitation system with proper error handling
- [ ] Migration scripts for existing team data

---

## Phase 5: Backend Integration Cleanup

### Priority: MEDIUM
**Timeline: Week 6-8**

#### 5.1 API Service Layer Implementation

**Current Issues:**
- Direct Supabase client calls scattered throughout components
- No consistent error handling
- Business logic mixed with UI components
- No request/response transformation

**Solution: Centralized API Service Architecture**

```typescript
// lib/api/ApiClient.ts
export class ApiClient {
  private supabase: SupabaseClient;
  private retryConfig: RetryConfig;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 5000
    };
  }

  async request<T>(operation: () => Promise<PostgrestResponse<T>>): Promise<T> {
    return this.withRetry(async () => {
      const response = await operation();
      
      if (response.error) {
        throw new ApiError(
          response.error.code || 'unknown_error',
          response.error.message,
          response.error.details
        );
      }

      return response.data;
    });
  }

  private async withRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === this.retryConfig.maxRetries || !this.isRetryable(error)) {
          break;
        }

        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(2, attempt),
          this.retryConfig.maxDelay
        );
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  private isRetryable(error: any): boolean {
    // Network errors, timeouts, and 5xx errors are retryable
    return error.code === 'NETWORK_ERROR' || 
           error.code === 'TIMEOUT' ||
           (error.status >= 500 && error.status < 600);
  }
}

// lib/api/ApiError.ts
export class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public details?: any,
    public status?: number
  ) {
    super(message);
    this.name = 'ApiError';
  }

  static fromSupabaseError(error: any): ApiError {
    return new ApiError(
      error.code || 'unknown_error',
      error.message || 'An unexpected error occurred',
      error.details,
      error.status
    );
  }
}
```

#### 5.2 Feature-Specific API Services

```typescript
// features/properties/services/PropertyService.ts
export class PropertyService {
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getProperties(teamId: string, filters?: PropertyFilters): Promise<Property[]> {
    let query = supabase
      .from('properties')
      .select(`
        *,
        bookings(count),
        inventory_items(count),
        maintenance_tasks(count)
      `)
      .eq('team_id', teamId);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters?.propertyType) {
      query = query.eq('property_type', filters.propertyType);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,address.ilike.%${filters.search}%`);
    }

    return this.apiClient.request(() => query);
  }

  async getProperty(id: string): Promise<PropertyDetail> {
    return this.apiClient.request(() =>
      supabase
        .from('properties')
        .select(`
          *,
          bookings(*),
          inventory_items(*),
          maintenance_tasks(*),
          damage_reports(*)
        `)
        .eq('id', id)
        .single()
    );
  }

  async createProperty(data: CreatePropertyDto): Promise<Property> {
    const propertyData = PropertyTransformer.toDatabase(data);
    
    return this.apiClient.request(() =>
      supabase
        .from('properties')
        .insert(propertyData)
        .select()
        .single()
    );
  }

  async updateProperty(id: string, data: UpdatePropertyDto): Promise<Property> {
    const updateData = PropertyTransformer.toDatabase(data);
    
    return this.apiClient.request(() =>
      supabase
        .from('properties')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()
    );
  }

  async deleteProperty(id: string): Promise<void> {
    await this.apiClient.request(() =>
      supabase
        .from('properties')
        .delete()
        .eq('id', id)
    );
  }
}

// Data Transfer Objects
export interface CreatePropertyDto {
  name: string;
  address: string;
  propertyType: PropertyType;
  bedrooms?: number;
  bathrooms?: number;
  squareFootage?: number;
  description?: string;
  amenities?: string[];
  images?: string[];
}

export interface UpdatePropertyDto extends Partial<CreatePropertyDto> {
  status?: PropertyStatus;
}

export interface PropertyFilters {
  status?: PropertyStatus;
  propertyType?: PropertyType;
  search?: string;
  limit?: number;
  offset?: number;
}
```

#### 5.3 Request/Response Transformers

```typescript
// features/properties/transformers/PropertyTransformer.ts
export class PropertyTransformer {
  static toDatabase(dto: CreatePropertyDto | UpdatePropertyDto): Partial<Database['public']['Tables']['properties']['Insert']> {
    return {
      name: dto.name,
      address: dto.address,
      property_type: dto.propertyType,
      bedrooms: dto.bedrooms,
      bathrooms: dto.bathrooms,
      square_footage: dto.squareFootage,
      description: dto.description,
      amenities: dto.amenities ? JSON.stringify(dto.amenities) : null,
      images: dto.images ? JSON.stringify(dto.images) : null,
      updated_at: new Date().toISOString()
    };
  }

  static fromDatabase(row: Database['public']['Tables']['properties']['Row']): Property {
    return {
      id: row.id,
      name: row.name,
      address: row.address,
      propertyType: row.property_type as PropertyType,
      bedrooms: row.bedrooms,
      bathrooms: row.bathrooms,
      squareFootage: row.square_footage,
      description: row.description,
      amenities: row.amenities ? JSON.parse(row.amenities) : [],
      images: row.images ? JSON.parse(row.images) : [],
      status: row.status as PropertyStatus,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  static toSummary(property: Property): PropertySummary {
    return {
      id: property.id,
      name: property.name,
      address: property.address,
      propertyType: property.propertyType,
      status: property.status,
      image: property.images?.[0]
    };
  }
}
```

#### 5.4 React Query Integration

```typescript
// features/properties/hooks/useProperties.ts
export const useProperties = (filters?: PropertyFilters) => {
  const { currentTeam } = useTeam();
  
  return useQuery({
    queryKey: ['properties', currentTeam?.id, filters],
    queryFn: () => {
      if (!currentTeam) throw new Error('No team selected');
      return propertyService.getProperties(currentTeam.id, filters);
    },
    enabled: !!currentTeam,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000,   // 30 minutes
  });
};

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: () => propertyService.getProperty(id),
    enabled: !!id,
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();
  const { currentTeam } = useTeam();

  return useMutation({
    mutationFn: (data: CreatePropertyDto) => propertyService.createProperty(data),
    onSuccess: () => {
      // Invalidate properties list
      queryClient.invalidateQueries({ 
        queryKey: ['properties', currentTeam?.id] 
      });
      
      // Show success message
      toast.success('Property created successfully');
    },
    onError: (error: ApiError) => {
      toast.error(error.message);
    }
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();
  const { currentTeam } = useTeam();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePropertyDto }) =>
      propertyService.updateProperty(id, data),
    onSuccess: (updatedProperty) => {
      // Update cached property
      queryClient.setQueryData(['property', updatedProperty.id], updatedProperty);
      
      // Invalidate properties list
      queryClient.invalidateQueries({ 
        queryKey: ['properties', currentTeam?.id] 
      });
      
      toast.success('Property updated successfully');
    },
    onError: (error: ApiError) => {
      toast.error(error.message);
    }
  });
};
```

#### 5.5 Global Error Handling

```typescript
// lib/errors/ErrorBoundary.tsx
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<
  { children: ReactNode; fallback?: ComponentType<{ error: Error; retry: () => void }> },
  ErrorBoundaryState
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Report to error tracking service
    if (process.env.NODE_ENV === 'production') {
      // trackError(error, errorInfo);
    }

    this.setState({ error, errorInfo });
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} retry={this.retry} />;
    }

    return this.props.children;
  }
}

// Error fallback component
const DefaultErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({
  error,
  retry
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center space-y-4">
        <AlertTriangle className="mx-auto h-12 w-12 text-red-500" />
        <h1 className="text-2xl font-bold text-gray-900">Something went wrong</h1>
        <p className="text-gray-600 max-w-md">
          {error.message || 'An unexpected error occurred. Please try again.'}
        </p>
        <div className="flex gap-2 justify-center">
          <Button onClick={retry}>Try Again</Button>
          <Button variant="ghost" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      </div>
    </div>
  );
};
```

### Phase 5 Deliverables

- [ ] Centralized API service layer with retry logic
- [ ] Feature-specific service classes
- [ ] Request/response transformers
- [ ] Integrated React Query patterns
- [ ] Global error handling system
- [ ] API documentation and usage examples

---

## Phase 6: Testing & Quality Assurance

### Priority: MEDIUM
**Timeline: Week 7-9**

#### 6.1 Testing Strategy & Framework Standardization

**Current Testing Issues:**
- Multiple test frameworks (Jest, Cypress, Playwright)
- Inconsistent test patterns
- Missing test coverage for critical paths
- No integration test strategy

**Standardized Testing Approach:**

```typescript
// tests/config/test-setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll } from 'vitest';
import { server } from './mocks/server';

// Start mock server
beforeAll(() => server.listen());

// Clean up after each test
afterEach(() => {
  cleanup();
  server.resetHandlers();
});

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
```

#### 6.2 Component Testing Standards

```typescript
// tests/utils/test-utils.tsx
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { TeamProvider } from '@/contexts/TeamContext';
import { BrowserRouter } from 'react-router-dom';

interface TestProvidersProps {
  children: React.ReactNode;
  initialTeam?: Team;
  initialUser?: User;
}

const TestProviders: React.FC<TestProvidersProps> = ({ 
  children, 
  initialTeam,
  initialUser 
}) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider initialUser={initialUser}>
          <TeamProvider initialTeam={initialTeam}>
            {children}
          </TeamProvider>
        </AuthProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: RenderOptions & {
    initialTeam?: Team;
    initialUser?: User;
  }
) => {
  const { initialTeam, initialUser, ...renderOptions } = options || {};
  
  return render(ui, {
    wrapper: ({ children }) => (
      <TestProviders initialTeam={initialTeam} initialUser={initialUser}>
        {children}
      </TestProviders>
    ),
    ...renderOptions,
  });
};

export * from '@testing-library/react';
export { customRender as render };
```

#### 6.3 API Testing & Mocking

```typescript
// tests/mocks/handlers.ts
import { http, HttpResponse } from 'msw';
import { mockProperties, mockTeams, mockUsers } from './data';

export const handlers = [
  // Properties API
  http.get('/rest/v1/properties', ({ request }) => {
    const url = new URL(request.url);
    const teamId = url.searchParams.get('team_id');
    
    const properties = mockProperties.filter(p => p.team_id === teamId);
    return HttpResponse.json(properties);
  }),

  http.post('/rest/v1/properties', async ({ request }) => {
    const body = await request.json();
    const newProperty = {
      id: crypto.randomUUID(),
      ...body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json(newProperty, { status: 201 });
  }),

  // Team management API
  http.get('/rest/v1/team_members', ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    
    const memberships = mockTeams
      .filter(team => team.members.some(m => m.user_id === userId))
      .map(team => ({
        team_id: team.id,
        user_id: userId,
        role: team.members.find(m => m.user_id === userId)?.role,
        team: team
      }));

    return HttpResponse.json(memberships);
  }),

  // Error scenarios
  http.get('/rest/v1/properties/:id', ({ params }) => {
    const property = mockProperties.find(p => p.id === params.id);
    
    if (!property) {
      return new HttpResponse(null, { 
        status: 404,
        statusText: 'Property not found'
      });
    }
    
    return HttpResponse.json(property);
  }),
];
```

#### 6.4 Integration Testing

```typescript
// tests/integration/property-management.test.tsx
import { render, screen, waitFor, userEvent } from '../utils/test-utils';
import { PropertyManagement } from '@/features/properties/components/PropertyManagement';
import { mockUser, mockTeam } from '../mocks/data';

describe('Property Management Integration', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Reset any existing state
    localStorage.clear();
  });

  it('should display properties for current team', async () => {
    render(<PropertyManagement />, {
      initialUser: mockUser,
      initialTeam: mockTeam
    });

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Property 1')).toBeInTheDocument();
      expect(screen.getByText('Property 2')).toBeInTheDocument();
    });
  });

  it('should create new property successfully', async () => {
    render(<PropertyManagement />, {
      initialUser: mockUser,
      initialTeam: mockTeam
    });

    // Click add property button
    await user.click(screen.getByRole('button', { name: /add property/i }));

    // Fill out form
    await user.type(screen.getByLabelText(/property name/i), 'New Property');
    await user.type(screen.getByLabelText(/address/i), '123 Test St');
    await user.selectOptions(screen.getByLabelText(/property type/i), 'house');

    // Submit form
    await user.click(screen.getByRole('button', { name: /save property/i }));

    // Verify success message
    await waitFor(() => {
      expect(screen.getByText(/property created successfully/i)).toBeInTheDocument();
    });

    // Verify property appears in list
    await waitFor(() => {
      expect(screen.getByText('New Property')).toBeInTheDocument();
    });
  });

  it('should handle permission-based access correctly', async () => {
    const guestUser = { ...mockUser, role: 'guest' };
    
    render(<PropertyManagement />, {
      initialUser: guestUser,
      initialTeam: mockTeam
    });

    // Guest users should not see add property button
    expect(screen.queryByRole('button', { name: /add property/i }))
      .not.toBeInTheDocument();

    // But should see property list
    await waitFor(() => {
      expect(screen.getByText('Property 1')).toBeInTheDocument();
    });
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    server.use(
      http.get('/rest/v1/properties', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );

    render(<PropertyManagement />, {
      initialUser: mockUser,
      initialTeam: mockTeam
    });

    // Should display error state
    await waitFor(() => {
      expect(screen.getByText(/failed to load properties/i)).toBeInTheDocument();
    });

    // Should have retry button
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
  });
});
```

#### 6.5 E2E Testing Strategy

```typescript
// tests/e2e/property-workflow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Property Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as property manager
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password123');
    await page.click('[data-testid=login-button]');
    
    // Wait for dashboard to load
    await expect(page.locator('[data-testid=dashboard]')).toBeVisible();
  });

  test('complete property creation and management workflow', async ({ page }) => {
    // Navigate to properties
    await page.click('[data-testid=nav-properties]');
    await expect(page).toHaveURL('/properties');

    // Create new property
    await page.click('[data-testid=add-property-button]');
    
    // Fill property details
    await page.fill('[data-testid=property-name]', 'E2E Test Property');
    await page.fill('[data-testid=property-address]', '123 Test Avenue');
    await page.selectOption('[data-testid=property-type]', 'apartment');
    await page.fill('[data-testid=bedrooms]', '2');
    await page.fill('[data-testid=bathrooms]', '1');

    // Upload property image
    await page.setInputFiles('[data-testid=image-upload]', 'tests/fixtures/property.jpg');
    
    // Save property
    await page.click('[data-testid=save-property]');
    
    // Verify success
    await expect(page.locator('[data-testid=success-message]')).toContainText('Property created');
    await expect(page.locator('[data-testid=property-card]')).toContainText('E2E Test Property');

    // Edit property
    await page.click('[data-testid=property-actions-menu]');
    await page.click('[data-testid=edit-property]');
    
    await page.fill('[data-testid=property-name]', 'Updated E2E Property');
    await page.click('[data-testid=save-property]');
    
    // Verify update
    await expect(page.locator('[data-testid=property-card]')).toContainText('Updated E2E Property');

    // Test property detail view
    await page.click('[data-testid=property-card]');
    await expect(page).toHaveURL(/\/properties\/[a-f0-9-]+/);
    await expect(page.locator('[data-testid=property-detail]')).toContainText('Updated E2E Property');
  });

  test('property search and filtering', async ({ page }) => {
    await page.goto('/properties');

    // Test search functionality
    await page.fill('[data-testid=property-search]', 'apartment');
    await page.keyboard.press('Enter');
    
    // Should show only apartments
    const propertyCards = page.locator('[data-testid=property-card]');
    await expect(propertyCards).toHaveCount(2); // Assuming 2 apartments in test data

    // Test filter by status
    await page.selectOption('[data-testid=status-filter]', 'active');
    
    // Should show only active properties
    await expect(propertyCards).toHaveCount(1);

    // Clear filters
    await page.click('[data-testid=clear-filters]');
    
    // Should show all properties
    await expect(propertyCards).toHaveCountGreaterThan(2);
  });
});
```

#### 6.6 Performance Testing

```typescript
// tests/performance/property-list.test.ts
import { render, screen } from '../utils/test-utils';
import { PropertyList } from '@/features/properties/components/PropertyList';
import { generateMockProperties } from '../mocks/generators';

describe('Property List Performance', () => {
  it('should render large property list efficiently', async () => {
    const largePropertyList = generateMockProperties(1000);
    
    const startTime = performance.now();
    
    render(<PropertyList properties={largePropertyList} />);
    
    // Wait for initial render
    await screen.findByText(largePropertyList[0].name);
    
    const renderTime = performance.now() - startTime;
    
    // Should render within reasonable time (< 100ms)
    expect(renderTime).toBeLessThan(100);
  });

  it('should handle rapid filter changes without performance degradation', async () => {
    const properties = generateMockProperties(500);
    const user = userEvent.setup();
    
    render(<PropertyManagement />);
    
    const searchInput = screen.getByLabelText(/search/i);
    
    // Rapid typing simulation
    const searches = ['ap', 'apa', 'apar', 'apart', 'apartm', 'apartme', 'apartmen', 'apartment'];
    
    const startTime = performance.now();
    
    for (const search of searches) {
      await user.clear(searchInput);
      await user.type(searchInput, search);
      // Small delay to simulate realistic typing
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    const totalTime = performance.now() - startTime;
    
    // Should handle rapid changes efficiently
    expect(totalTime).toBeLessThan(1000);
  });
});
```

### Phase 6 Deliverables

- [ ] Standardized testing framework (Vitest + Testing Library)
- [ ] Comprehensive component test coverage (>80%)
- [ ] Integration tests for critical user flows
- [ ] E2E tests for main application workflows
- [ ] Performance testing suite
- [ ] CI/CD pipeline with automated testing

---

## Phase 7: Performance & Production Optimization

### Priority: LOW
**Timeline: Week 8-10**

#### 7.1 Bundle Optimization

**Current Bundle Issues:**
- Large bundle size with unused dependencies
- No code splitting strategy
- Inefficient asset loading
- Missing optimization for production builds

**Bundle Analysis & Optimization:**

```typescript
// vite.config.ts - Optimized build configuration
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  build: {
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-select'],
          supabase: ['@supabase/supabase-js'],
          utils: ['date-fns', 'zod', 'clsx'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  }
});
```

#### 7.2 Code Splitting Strategy

```typescript
// app/router/routes.tsx
import { lazy } from 'react';
import { LoadingSpinner } from '@/shared/components/ui/LoadingSpinner';

// Lazy load feature modules
const Dashboard = lazy(() => import('@/features/dashboard/pages/Dashboard'));
const Properties = lazy(() => import('@/features/properties/pages/Properties'));
const PropertyDetail = lazy(() => import('@/features/properties/pages/PropertyDetail'));
const Inventory = lazy(() => import('@/features/inventory/pages/Inventory'));
const Maintenance = lazy(() => import('@/features/maintenance/pages/Maintenance'));
const TeamManagement = lazy(() => import('@/features/teams/pages/TeamManagement'));

// Route-based code splitting
export const routes = [
  {
    path: '/dashboard',
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Dashboard />
      </Suspense>
    ),
  },
  {
    path: '/properties',
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Properties />
      </Suspense>
    ),
  },
  {
    path: '/properties/:id',
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <PropertyDetail />
      </Suspense>
    ),
  },
  // ... other routes
];

// Dynamic imports for large components
export const DynamicDataTable = lazy(() => 
  import('@/shared/components/business/DataTable').then(module => ({
    default: module.DataTable
  }))
);

export const DynamicImageUpload = lazy(() =>
  import('@/shared/components/business/ImageUpload').then(module => ({
    default: module.ImageUpload
  }))
);
```

#### 7.3 Performance Monitoring

```typescript
// lib/performance/PerformanceMonitor.ts
export class PerformanceMonitor {
  private static observers: Map<string, PerformanceObserver> = new Map();

  static initialize() {
    if (typeof window === 'undefined') return;

    // Core Web Vitals monitoring
    this.observeWebVitals();
    
    // Custom performance metrics
    this.observeResourceTiming();
    this.observeUserTiming();
  }

  private static observeWebVitals() {
    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      this.reportMetric('LCP', lastEntry.startTime);
    });
    lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const firstInput = list.getEntries()[0];
      if (firstInput) {
        const fid = firstInput.processingStart - firstInput.startTime;
        this.reportMetric('FID', fid);
      }
    });
    fidObserver.observe({ type: 'first-input', buffered: true });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      this.reportMetric('CLS', clsValue);
    });
    clsObserver.observe({ type: 'layout-shift', buffered: true });

    this.observers.set('webVitals', lcpObserver);
  }

  private static observeResourceTiming() {
    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.initiatorType === 'fetch') {
          const duration = entry.responseEnd - entry.requestStart;
          this.reportMetric('API_REQUEST_DURATION', duration, {
            url: entry.name,
            method: (entry as any).method || 'GET'
          });
        }
      });
    });

    resourceObserver.observe({ type: 'resource', buffered: true });
    this.observers.set('resource', resourceObserver);
  }

  private static reportMetric(name: string, value: number, labels?: Record<string, string>) {
    // Report to analytics service
    if (process.env.NODE_ENV === 'production') {
      // Example: Analytics service integration
      // analytics.track('performance_metric', {
      //   metric: name,
      //   value,
      //   ...labels
      // });
    }

    console.debug(`Performance Metric: ${name}`, { value, labels });
  }

  static measureOperation<T>(name: string, operation: () => T): T {
    const startTime = performance.now();
    
    try {
      const result = operation();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - startTime;
          this.reportMetric(`OPERATION_${name.toUpperCase()}`, duration);
        }) as T;
      }
      
      const duration = performance.now() - startTime;
      this.reportMetric(`OPERATION_${name.toUpperCase()}`, duration);
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.reportMetric(`OPERATION_${name.toUpperCase()}_ERROR`, duration);
      throw error;
    }
  }
}

// Usage in components
export const usePerformanceTracking = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const mountDuration = performance.now() - startTime;
      PerformanceMonitor.reportMetric(`COMPONENT_MOUNT_${componentName.toUpperCase()}`, mountDuration);
    };
  }, [componentName]);
};
```

#### 7.4 Database Query Optimization

```typescript
// lib/database/QueryOptimizer.ts
export class QueryOptimizer {
  private static queryCache = new Map<string, { query: string; timestamp: number }>();

  static optimizeQuery(baseQuery: PostgrestQueryBuilder<any>, options: {
    select?: string;
    limit?: number;
    offset?: number;
    orderBy?: { column: string; ascending: boolean };
    filters?: Record<string, any>;
  }) {
    let query = baseQuery;

    // Optimize select fields - only fetch what's needed
    if (options.select) {
      query = query.select(options.select);
    }

    // Add efficient filtering
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'string' && value.includes('%')) {
            query = query.ilike(key, value);
          } else {
            query = query.eq(key, value);
          }
        }
      });
    }

    // Add pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    // Add ordering
    if (options.orderBy) {
      query = query.order(options.orderBy.column, { ascending: options.orderBy.ascending });
    }

    return query;
  }

  static async executeCachedQuery<T>(
    queryKey: string,
    queryFn: () => Promise<T>,
    cacheTime = 5 * 60 * 1000 // 5 minutes
  ): Promise<T> {
    const cached = this.queryCache.get(queryKey);
    
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return JSON.parse(cached.query);
    }

    const result = await queryFn();
    
    this.queryCache.set(queryKey, {
      query: JSON.stringify(result),
      timestamp: Date.now()
    });

    return result;
  }
}

// Optimized property queries
export class OptimizedPropertyService extends PropertyService {
  async getPropertiesSummary(teamId: string, page = 0, limit = 20): Promise<PropertySummary[]> {
    const queryKey = `properties_summary_${teamId}_${page}_${limit}`;
    
    return QueryOptimizer.executeCachedQuery(queryKey, async () => {
      const query = QueryOptimizer.optimizeQuery(
        supabase.from('properties'),
        {
          select: 'id, name, address, property_type, status, images, created_at',
          limit,
          offset: page * limit,
          orderBy: { column: 'created_at', ascending: false },
          filters: { team_id: teamId }
        }
      );

      const { data, error } = await query;
      
      if (error) throw error;
      
      return data.map(PropertyTransformer.toSummary);
    });
  }

  async getPropertyWithStats(id: string): Promise<PropertyWithStats> {
    const queryKey = `property_with_stats_${id}`;
    
    return QueryOptimizer.executeCachedQuery(queryKey, async () => {
      const { data, error } = await supabase
        .from('properties')
        .select(`
          *,
          inventory_count:inventory_items(count),
          maintenance_count:maintenance_tasks(count),
          damage_count:damage_reports(count),
          booking_count:bookings(count)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      return PropertyTransformer.toPropertyWithStats(data);
    }, 2 * 60 * 1000); // 2 minutes cache for detailed view
  }
}
```

#### 7.5 Image Optimization

```typescript
// lib/images/ImageOptimizer.ts
export class ImageOptimizer {
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly QUALITY = 0.8;

  static async optimizeImage(file: File): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate optimal dimensions
        const { width, height } = this.calculateOptimalDimensions(
          img.naturalWidth,
          img.naturalHeight
        );

        canvas.width = width;
        canvas.height = height;

        // Draw and compress image
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to optimize image'));
              return;
            }

            const optimizedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });

            resolve(optimizedFile);
          },
          'image/jpeg',
          this.QUALITY
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  private static calculateOptimalDimensions(width: number, height: number) {
    const maxWidth = 1920;
    const maxHeight = 1080;

    if (width <= maxWidth && height <= maxHeight) {
      return { width, height };
    }

    const aspectRatio = width / height;

    if (width > height) {
      return {
        width: maxWidth,
        height: Math.floor(maxWidth / aspectRatio),
      };
    } else {
      return {
        width: Math.floor(maxHeight * aspectRatio),
        height: maxHeight,
      };
    }
  }

  static async generateThumbnail(file: File, size = 200): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = size;
        canvas.height = size;

        // Calculate crop dimensions for square thumbnail
        const minDimension = Math.min(img.naturalWidth, img.naturalHeight);
        const x = (img.naturalWidth - minDimension) / 2;
        const y = (img.naturalHeight - minDimension) / 2;

        ctx?.drawImage(
          img,
          x, y, minDimension, minDimension,
          0, 0, size, size
        );

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to generate thumbnail'));
              return;
            }

            const thumbnailFile = new File([blob], `thumb_${file.name}`, {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });

            resolve(thumbnailFile);
          },
          'image/jpeg',
          0.9
        );
      };

      img.onerror = () => reject(new Error('Failed to load image for thumbnail'));
      img.src = URL.createObjectURL(file);
    });
  }
}
```

#### 7.6 Production Environment Setup

```typescript
// config/production.ts
export const productionConfig = {
  api: {
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  
  cache: {
    staleTime: 5 * 60 * 1000,    // 5 minutes
    gcTime: 30 * 60 * 1000,      // 30 minutes
    maxEntries: 1000,
  },

  performance: {
    enableMonitoring: true,
    sampleRate: 0.1, // 10% of users
    reportInterval: 60000, // 1 minute
  },

  security: {
    enforceHttps: true,
    hsts: true,
    contentSecurityPolicy: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: [
        "'self'",
        "https://pwaeknalhosfwuxkpaet.supabase.co",
        "wss://pwaeknalhosfwuxkpaet.supabase.co"
      ],
    },
  },

  logging: {
    level: 'warn',
    enableConsole: false,
    enableRemote: true,
  },
};

// Environment-specific service worker
// public/sw-production.js
const CACHE_NAME = 'stayfuse-v1.0.0';
const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(STATIC_ASSETS))
  );
});

self.addEventListener('fetch', (event) => {
  // Cache-first strategy for static assets
  if (event.request.url.includes('/static/')) {
    event.respondWith(
      caches.match(event.request)
        .then(response => response || fetch(event.request))
    );
    return;
  }

  // Network-first strategy for API calls
  if (event.request.url.includes('/rest/')) {
    event.respondWith(
      fetch(event.request)
        .catch(() => caches.match(event.request))
    );
    return;
  }
});
```

### Phase 7 Deliverables

- [ ] Optimized bundle size (target: <1MB gzipped)
- [ ] Implemented code splitting for all major routes
- [ ] Performance monitoring dashboard
- [ ] Database query optimization (50% faster queries)
- [ ] Image optimization pipeline
- [ ] Production-ready deployment configuration
- [ ] Performance benchmarks and monitoring

---

## Implementation Timeline

### Week 1: Testing Foundation & Safety Infrastructure (Phase 0)
- [ ] Fix broken Jest configuration 
- [ ] Create comprehensive functionality snapshots
- [ ] Set up proper API mocking for Supabase
- [ ] Implement automated regression detection
- [ ] Create feature flag system for safe rollouts

### Week 2-3: Foundation Cleanup (Phase 1)
- [ ] Root directory reorganization (with testing after each step)
- [ ] Documentation consolidation (99 → 15 files)
- [ ] Test structure organization  
- [ ] Remove obsolete files and artifacts

### Week 3-4: Database & Schema Optimization
- [ ] Schema analysis and normalization
- [ ] RLS policy optimization
- [ ] Performance indexes implementation
- [ ] Data migration scripts

### Week 4-6: Component System Modernization
- [ ] Design system foundation
- [ ] Universal components (Button, Form, Modal, DataTable)
- [ ] Image upload standardization
- [ ] Component documentation

### Week 5-7: Team Management Restructure
- [ ] Simplified team architecture
- [ ] Permission system implementation
- [ ] Team context provider
- [ ] Invitation system improvement

### Week 6-8: Backend Integration Cleanup
- [ ] API service layer implementation
- [ ] Request/response transformers
- [ ] Error handling standardization
- [ ] React Query optimization

### Week 7-9: Testing & Quality Assurance
- [ ] Test framework standardization
- [ ] Component test coverage (>80%)
- [ ] Integration testing suite
- [ ] E2E test implementation

### Week 8-10: Performance & Production Optimization
- [ ] Bundle optimization
- [ ] Code splitting implementation
- [ ] Performance monitoring
- [ ] Production deployment setup

### Week 10-12: Final Integration & Deployment
- [ ] System integration testing
- [ ] Performance benchmarking
- [ ] Production deployment
- [ ] Documentation finalization

---

## Risk Assessment & Mitigation

### ✅ RISKS SIGNIFICANTLY REDUCED (Thanks to Local Supabase Setup)

#### 1. Production Data Safety - RISK ELIMINATED ✅
**Previous Risk**: Production data loss with 9,675+ auth records
**Current Status**: **RISK ELIMINATED** - Using isolated local Supabase
**Why It's Safe Now**:
- ✅ **Local isolation confirmed** - Test created maintenance task, stayed local only
- ✅ **Real data copy** - All 9,675+ auth records and business data available locally
- ✅ **Zero production connection** - Changes never sync to production database
- ✅ **Easy reset** - `supabase db reset` restores original state instantly
- ✅ **Full experimentation freedom** - Can safely test all schema changes

#### 2. Authentication System Changes - RISK GREATLY REDUCED ✅
**Previous Risk**: Breaking auth system for 9,675+ users
**Current Status**: **RISK GREATLY REDUCED** - Local testing with real auth data
**Why It's Much Safer Now**:
- ✅ **Test with real auth data** - 9,675+ real auth records available locally
- ✅ **Safe experimentation** - Can modify auth system locally without affecting production
- ✅ **Real user testing** - <EMAIL> credentials work in local environment
- ✅ **Instant rollback** - `supabase db reset` if auth changes break
- ✅ **Production deployment only after local verification**

#### 3. Missing Critical Dependencies
**Risk**: App won't build/run after cleanup due to removed dependencies
**Likelihood**: HIGH - Many unused dependencies to remove
**Impact**: HIGH - App completely broken
**Mitigation**:
- ✅ Test build after every dependency change
- ✅ Use dependency analysis tools before removal
- ✅ Staged dependency removal with testing
- ✅ Keep detailed log of all removed dependencies

### High Risk Items

#### 4. Component Migration Breaking UI
**Risk**: Users can't complete critical workflows (property management, etc.)
**Likelihood**: MEDIUM
**Impact**: HIGH
**Mitigation**: 
- ✅ Visual regression testing with screenshots
- ✅ Functional testing of all user workflows
- ✅ Feature flags for gradual component rollout
- ✅ Immediate rollback capability

#### 5. Data Migration Complexity
**Risk**: Complex schema changes could cause data loss or corruption
**Likelihood**: MEDIUM  
**Impact**: CATASTROPHIC
**Mitigation**: 
- ✅ Comprehensive backup strategy before any migrations
- ✅ Staged migration approach with rollback procedures
- ✅ Extensive testing on production data copy
- ✅ Blue-green deployment strategy

#### 2. Breaking Changes During Refactoring
**Risk**: Component refactoring could break existing functionality
**Mitigation**:
- Gradual migration with backward compatibility
- Comprehensive test coverage before refactoring
- Feature flagging for new components
- Staged rollout with monitoring

#### 3. Team Resistance to New Patterns
**Risk**: Development team may resist new architectural patterns
**Mitigation**:
- Clear documentation and training materials
- Gradual introduction of new patterns
- Show clear benefits and improvements
- Pair programming during transition

### Medium Risk Items

#### 4. Performance Regressions
**Risk**: New abstractions could impact performance
**Mitigation**:
- Performance benchmarking before and after changes
- Continuous performance monitoring
- Profiling tools integration
- Performance budgets and alerts

#### 5. Third-party Dependencies
**Risk**: Dependencies may have security vulnerabilities or become deprecated
**Mitigation**:
- Regular dependency audits
- Automated security scanning
- Version pinning and controlled updates
- Alternative dependency research

### Low Risk Items

#### 6. Documentation Maintenance
**Risk**: New documentation may become outdated
**Mitigation**:
- Automated documentation generation where possible
- Regular documentation review cycles
- Clear ownership and responsibility
- Integration with development workflow

---

## Success Metrics & KPIs

### Code Quality Metrics

#### Before Cleanup
- **Files**: 444 TypeScript/React files
- **Documentation**: 99 markdown files
- **Components**: 150+ components with duplicates
- **Bundle Size**: ~3.2MB (estimated)
- **Test Coverage**: <40%
- **Build Time**: ~2-3 minutes

#### Target After Cleanup
- **Files**: <300 TypeScript/React files (32% reduction)
- **Documentation**: 15 organized files (85% reduction)
- **Components**: <100 standardized components
- **Bundle Size**: <1MB gzipped (70% reduction)
- **Test Coverage**: >80%
- **Build Time**: <1 minute (67% improvement)

### Performance Metrics

#### Core Web Vitals Targets
- **Largest Contentful Paint (LCP)**: < 2.5s
- **First Input Delay (FID)**: < 100ms
- **Cumulative Layout Shift (CLS)**: < 0.1

#### Database Performance
- **Query Response Time**: < 200ms (95th percentile)
- **Database Connection Pool Utilization**: < 70%
- **Failed Query Rate**: < 0.1%

#### User Experience Metrics
- **Page Load Time**: < 3s
- **Time to Interactive**: < 5s
- **Error Rate**: < 1%
- **User Session Duration**: +25% improvement

### Developer Experience Metrics

#### Development Velocity
- **New Feature Development Time**: -40%
- **Bug Fix Time**: -50%
- **Code Review Time**: -30%
- **Onboarding Time for New Developers**: -60%

#### Code Maintainability
- **Cyclomatic Complexity**: < 10 (average)
- **Code Duplication**: < 5%
- **Technical Debt Ratio**: < 20%
- **Documentation Coverage**: > 90%

### Business Impact Metrics

#### System Reliability
- **Uptime**: > 99.9%
- **Mean Time to Recovery (MTTR)**: < 15 minutes
- **Deployment Success Rate**: > 95%
- **Customer-Reported Issues**: -70%

#### Cost Optimization
- **Infrastructure Costs**: -25% (through optimization)
- **Development Costs**: -35% (through efficiency)
- **Maintenance Costs**: -50% (through automation)

---

## Conclusion

This comprehensive cleanup and modernization plan addresses all critical issues in the StayFuse codebase:

1. **Immediate Impact**: Foundation cleanup will provide instant improvements in developer experience and codebase navigation

2. **Long-term Benefits**: Component standardization and architectural improvements will enable faster feature development and easier maintenance

3. **Production Readiness**: Performance optimization and testing improvements will ensure the application can scale reliably

4. **Team Productivity**: Standardized patterns and improved documentation will reduce onboarding time and development friction

5. **Technical Debt Reduction**: Systematic cleanup will eliminate accumulated technical debt and prevent future accumulation

The plan is designed to be executed incrementally, allowing the team to continue feature development while gradually improving the codebase. Each phase builds upon the previous one, ensuring steady progress toward a maintainable, production-ready application.

**Expected Timeline**: 10-12 weeks for complete implementation
**Expected ROI**: 40% reduction in development time, 70% reduction in bugs, 50% faster feature delivery

---

## 🚨 CRITICAL SUCCESS CHECKLIST

Before declaring this cleanup successful, ALL of these must be verified:

### Data Integrity ✅ (Local Supabase with Production Data Copy)
- [ ] All 9,675+ auth records preserved in LOCAL database  
- [ ] All 18 profiles intact with correct permissions (LOCAL)
- [ ] All 66 database tables functional in LOCAL Supabase
- [ ] All user data (properties, inventory, maintenance) preserved locally
- [ ] Authentication system works with LOCAL auth.users table
- [ ] **CONFIRMED**: Local changes stay local (isolation verified)

### Functionality Preservation ✅ (Using Local Supabase)
- [ ] Local Supabase services running (ports 54321-54324)
- [ ] Login <NAME_EMAIL> / Newsig1!!! (LOCAL auth)
- [ ] Dashboard loads with real production data (from LOCAL database)
- [ ] Property management (CRUD) works with LOCAL data
- [ ] Inventory management functional with LOCAL data
- [ ] Maintenance system operational with LOCAL tasks
- [ ] Team management works with LOCAL team data
- [ ] File uploads work with LOCAL storage
- [ ] Edge Functions work locally (ai-command-processor confirmed)

### Performance Requirements ✅
- [ ] App builds successfully (`npm run build`)
- [ ] Dev server starts without errors (`npm run dev`)
- [ ] Page load times ≤ previous performance
- [ ] Database queries perform at same speed or better
- [ ] No new console errors or warnings

### Code Quality Achieved ✅
- [ ] File count reduced from 444 to <300 TypeScript files
- [ ] Documentation reduced from 99 to 15 organized files
- [ ] Component count reduced with no duplicate UI patterns
- [ ] Test coverage >80% for all modified code
- [ ] All linting and type-checking passes

### Safety Measures in Place ✅
- [ ] All critical scripts created and tested (see MISSING_SCRIPTS_FOR_CLEANUP.md)
- [ ] Complete database backups verified restorable
- [ ] Rollback procedures tested and documented
- [ ] Feature flags system operational
- [ ] Automated regression detection working

### Production Readiness ✅
- [ ] Bundle size <1MB gzipped
- [ ] Core Web Vitals within targets (LCP <2.5s, FID <100ms, CLS <0.1)
- [ ] Error handling graceful and user-friendly
- [ ] API layer consistent and documented
- [ ] Security best practices implemented

## ⚠️ FAILURE CONDITIONS (STOP IMMEDIATELY IF ANY OCCUR)

- ❌ Unable to login with test credentials
- ❌ Any data loss detected in database
- ❌ App won't build or start
- ❌ Critical user workflows broken
- ❌ Performance degradation >20%
- ❌ New errors introduced that weren't there before

## 🎯 SUCCESS DECLARATION

**This cleanup is successful ONLY when:**
1. Every item in the Critical Success Checklist is ✅
2. Zero failure conditions have occurred
3. The app works identically to the original but with cleaner code
4. All stakeholders can perform their normal workflows without issues

**Remember**: The goal is a cleaner, more maintainable codebase with ZERO functionality loss. If any functionality is broken, the cleanup has failed and must be rolled back.

---

This transformation will position StayFuse as a modern, scalable, and maintainable application ready for production use and future growth.