import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.2.1";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { generateIntelligentSuggestions, findSimilarItems, suggestProperties } from "./intelligentSuggestions.ts";
const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") || "AIzaSyD55Kn_94EdiW7czvu8qZ4G6R76vRL563s";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};
const handler = async (req)=>{
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY is not set");
    }
    if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase environment variables are not set");
    }
    const requestBody = await req.json();
    console.log("Request body:", requestBody);
    const { command } = requestBody;
    if (!command || command.trim() === "") {
      throw new Error("Command is required");
    }

    // Extract user ID from JWT token for security
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      throw new Error("Authorization header is required");
    }

    // Initialize Supabase client with anon key to verify JWT
    const supabaseAuth = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    const token = authHeader.replace('Bearer ', '');

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser(token);
    if (authError || !user) {
      console.error("Auth error:", authError);
      throw new Error("Invalid or expired token");
    }

    const userId = user.id;
    console.log("Processing command:", command);
    console.log("Authenticated user ID:", userId);
    console.log("User email:", user.email);
    console.log("Full user object:", JSON.stringify(user, null, 2));

    // Initialize Supabase client with service role key for admin access
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    // Initialize the Gemini API
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      generationConfig: {
        temperature: 0.2,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 8192
      }
    });
    // Get user's data for context (including team properties)
    const { data: userProperties, error: propertiesError } = await supabase
      .from("properties")
      .select(`
        id, name, address, city, state, zip, bedrooms, bathrooms,
        team_properties!inner(
          team_id,
          team_members!inner(
            user_id,
            status
          )
        )
      `)
      .eq("team_properties.team_members.user_id", userId)
      .eq("team_properties.team_members.status", "active");
    if (propertiesError) {
      console.error("Error fetching properties:", propertiesError);
    }
    // Get available team members and service providers for assignee context
    const { data: teamMembers, error: teamMembersError } = await supabase.from("profiles").select("id, first_name, last_name, email, role").in("role", [
      "service_provider",
      "staff",
      "property_manager",
      "admin"
    ]);
    if (teamMembersError) {
      console.error("Error fetching team members:", teamMembersError);
    }
    const { data: collections, error: collectionsError } = await supabase.from("collections").select("id, name").eq("user_id", userId);
    if (collectionsError) {
      console.error("Error fetching collections:", collectionsError);
    }
    // Get inventory items for context
    const { data: inventoryItems, error: inventoryError } = await supabase.from("inventory_items").select(`
        id, name, quantity, min_quantity, collection,
        properties:property_id (id, name)
      `).eq("user_id", userId);
    if (inventoryError) {
      console.error("Error fetching inventory items:", inventoryError);
    }
    // Format inventory items for better context
    const formattedInventoryItems = inventoryItems?.map((item)=>({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
        minQuantity: item.min_quantity,
        collection: item.collection,
        propertyId: item.properties?.id,
        propertyName: item.properties?.name,
        isLowStock: item.quantity <= item.min_quantity
      }));
    // Prepare the prompt with the user's context
    const prompt = `
    You are a property management assistant that helps users manage their vacation rental properties.

    Your job is to understand the user's command and convert it into a specific action in our system.

    User data context:
    - Properties: ${JSON.stringify(userProperties || [])}
    - Collections: ${JSON.stringify(collections || [])}
    - Inventory Items: ${JSON.stringify(formattedInventoryItems || [])}
    - Available Assignees: ${JSON.stringify(teamMembers?.map((tm)=>({
        id: tm.id,
        name: `${tm.first_name} ${tm.last_name}`,
        email: tm.email,
        role: tm.role
      })) || [])}

    The system supports the following actions:
    1. Add a property (needs name, address, city, state, zip, and optionally bedrooms and bathrooms)
    2. Create a collection (needs name and optionally budget)
    3. Add a maintenance task (needs title, property, and optionally severity, description, due date, assignee)
    4. Add inventory items (needs name, quantity, property, and optionally collection, min_quantity, price, image_url) - ONLY use this if the item doesn't already exist
    5. Update inventory item (needs item name or id, and the new quantity and/or min_quantity) - ALWAYS use this if a similar item already exists
    6. Create purchase order (needs items to include, can be "all low stock items" or specific items by name)

    IMPORTANT RULES FOR INVENTORY MANAGEMENT:
    1. FIRST, determine if the item exists in inventory:
       - If the item exists: use updateInventoryItem to modify quantities
       - If the item does NOT exist: use addInventoryItem to create it
    2. For similarity matching, consider singular/plural forms (e.g., "towel" and "towels" are the same), common variations (e.g., "bath towel" and "bathroom towel"), and ignore case.
    3. Commands like "Add X to property" or "We need X" should:
       - Use addInventoryItem if X doesn't exist in inventory yet
       - Use updateInventoryItem if X already exists in inventory
    4. Commands like "We need more X", "Add Z more X", "Increase X by Z", or "We're running low on X" should:
       - Use updateInventoryItem if X exists (to increase quantity)
       - Use addInventoryItem if X doesn't exist (to create it with the specified quantity)
    5. If the user mentions a specific property, make sure to match the inventory item for that property.
    6. If multiple similar items exist, prefer exact matches over partial matches, and prefer matching by property if specified.
    7. For quantity updates, if the user says "add 5 more towels", this means to INCREASE the current quantity by 5, not set it to 5.
    8. If the user doesn't specify a quantity for an update, assume they want to increase by a reasonable amount (e.g., 5 for consumables, 1-2 for durable goods).
    9. If the user mentions a minimum quantity (e.g., "we need at least 10 towels"), set both the current quantity and min_quantity accordingly.

    PROPERTY MATCHING RULES:
    - ALWAYS use the EXACT property name from the Properties list above, never use the user's shortened version
    - Be flexible with matching: "True" should match "20235 True Rd (Andy)", "Thames" should match "Thames (Andy)"
    - When user says "True", find the property containing "True" and use its FULL name: "20235 True Rd (Andy)"
    - When user says "Thames", find the property containing "Thames" and use its FULL name: "Thames (Andy)"
    - For addresses, match key parts: "True" matches "20235 True Rd (Andy)" because "True" is in the address
    - Property names in parentheses (like "Thames (Andy)") should match when user just says the main part ("Thames")
    - If multiple properties could match, prefer the one with the most specific match

    EXAMPLES OF INVENTORY ACTIONS:
    - "Add 3 towels to Beach House" → addInventoryItem if towels don't exist, updateInventoryItem if they do
    - "We need more towels at Beach House" → updateInventoryItem if towels exist, addInventoryItem if they don't
    - "Add 3 more wine glasses" → updateInventoryItem (assumes wine glasses already exist)
    - "We're down to only 2 toilet paper rolls" → updateInventoryItem with name="toilet paper", set quantity=2
    - "Increase the minimum stock of paper towels to 10" → updateInventoryItem with name="paper towels", set min_quantity=10
    - "We're running low on dish soap" → updateInventoryItem if exists, addInventoryItem if new

    EXAMPLES OF PURCHASE ORDER CREATION:
    - "Create a purchase order for all low stock items" → createPurchaseOrder with allLowStock=true
    - "Create a PO for all items that need restocking" → createPurchaseOrder with allLowStock=true
    - "Make a purchase order for towels and toilet paper" → createPurchaseOrder with items=["towels", "toilet paper"]
    - "Order more cleaning supplies" → createPurchaseOrder with items matching cleaning supplies
    - "Create purchase order for low stock items at Beach House" → createPurchaseOrder with allLowStock=true, property="Beach House"
    - "Order supplies for Ocean View property" → createPurchaseOrder with allLowStock=true, property="Ocean View"
    - "Create PO for bathroom supplies" → createPurchaseOrder with items matching bathroom supplies
    - "Order everything we need for the downtown apartment" → createPurchaseOrder with allLowStock=true, property="downtown apartment"

    EXAMPLES OF MAINTENANCE TASK CREATION:
    - "Fix the broken window at Beach House" → addMaintenanceTask with title="Fix broken window", property="Beach House", severity="medium"
    - "Replace the light bulb in the kitchen at Ocean View, make it high priority" → addMaintenanceTask with title="Replace light bulb", property="Ocean View", severity="high"
    - "Clean the pool at the downtown apartment, due next Friday" → addMaintenanceTask with title="Clean pool", property="downtown apartment", dueDate="next Friday"
    - "There is a broken window at Thames, assign to Ashlee Glass and make it due on March 23rd" → addMaintenanceTask with title="Fix broken window", property="Thames", assignee="Ashlee Glass", dueDate="March 23rd"

    EXAMPLE JSON RESPONSE for "Fix the door, assign to Ashlee Glass":
    {
      "action": "addMaintenanceTask",
      "data": {
        "title": "Fix door",
        "assignee": "Ashlee Glass"
      },
      "message": "I'll create a maintenance task to fix the door and assign it to Ashlee Glass."
    }

    EXAMPLE JSON RESPONSE for "add 3 towels to Beach House":
    {
      "action": "addInventoryItem",
      "data": {
        "name": "towels",
        "quantity": 3,
        "property": "Beach House",
        "collection": "General"
      },
      "message": "I'll add 3 towels to Beach House inventory."
    }

    EXAMPLE JSON RESPONSE for "add 1 fly swatter to Thames inventory":
    {
      "action": "addInventoryItem",
      "data": {
        "name": "fly swatter",
        "quantity": 1,
        "property": "Thames",
        "collection": "General"
      },
      "message": "I'll add 1 fly swatter to Thames inventory."
    }

    EXAMPLE JSON RESPONSE for "we need 1 fly swatter at True" (where "True" should match "20235 True Rd (Andy)"):
    {
      "action": "addInventoryItem",
      "data": {
        "name": "fly swatter",
        "quantity": 1,
        "property": "20235 True Rd (Andy)",
        "collection": "General"
      },
      "message": "I'll add 1 fly swatter to 20235 True Rd (Andy) property."
    }

    EXAMPLE JSON RESPONSE for "Create a purchase order for all low stock items":
    {
      "action": "createPurchaseOrder",
      "data": {
        "allLowStock": true
      },
      "message": "I'll create a purchase order for all low stock items."
    }

    EXAMPLE JSON RESPONSE for "Create a purchase order for towels and toilet paper":
    {
      "action": "createPurchaseOrder",
      "data": {
        "items": ["towels", "toilet paper"]
      },
      "message": "I'll create a purchase order for towels and toilet paper."
    }

    ANOTHER EXAMPLE for "broken door handle at c st, assign to Ashlee glass":
    {
      "action": "addMaintenanceTask",
      "data": {
        "title": "Broken door handle",
        "property": "c st",
        "assignee": "Ashlee glass"
      },
      "message": "Successfully added maintenance task 'Broken door handle' for c st and assigned to Ashlee glass."
    }
    - "Repair the leaky faucet, assign it to John Smith" → addMaintenanceTask with title="Repair leaky faucet", assignee="John Smith"
    - "Schedule HVAC maintenance for next week, assign to Mike Johnson" → addMaintenanceTask with title="HVAC maintenance", assignee="Mike Johnson", dueDate="next week"
    - "Fix the broken door handle, assign to maintenance team" → addMaintenanceTask with title="Fix broken door handle", assignee="maintenance team"

    Based on the user's command, determine what action they want to take and extract the necessary information.

    Respond with a JSON object that contains:
    - action: The action to take (addProperty, createCollection, addMaintenanceTask, addInventoryItem, updateInventoryItem, createPurchaseOrder)
    - data: An object containing the extracted information needed for the action
    - message: A user-friendly message describing what will be done

    For addMaintenanceTask, the data object MUST include these fields:
    - title: The task title (required)
    - description: Task description (optional)
    - property: Property name (optional)
    - severity: "low", "medium", "high", or "critical" (optional, defaults to "medium")
    - dueDate: Due date if mentioned (optional)
    - assignee: Name of person to assign to if mentioned (IMPORTANT: always include this field, set to null if no assignee mentioned)

    CRITICAL: When someone says "assign to [name]", you MUST include "assignee": "[name]" in the data object.

    If you cannot understand the command or it doesn't match any of the supported actions, analyze what the user might be trying to do and provide intelligent suggestions. Consider these scenarios:

    - If they mention items/inventory: suggest inventory-related commands
    - If they mention repairs/broken/fix: suggest maintenance commands
    - If they mention damage/stain/broken: suggest damage report commands
    - If they mention property names: suggest property-related actions
    - If they mention buying/ordering: suggest purchase order commands
    - If they mention staff/assign: suggest assignment commands
    - If they mention urgent/emergency: suggest priority actions

    Respond with:
    {
      "action": "unknown",
      "message": "I understand you want to [infer their intent], but I need more details. Here's what I can help with:",
      "suggestions": [
        "Based on your request, try: [specific suggestion 1]",
        "Or maybe you meant: [specific suggestion 2]",
        "You could also: [specific suggestion 3]"
      ]
    }

    The command from the user is: "${command}"

    Only return the JSON object, nothing else.
    `;
    // Call the Gemini API
    console.log("Sending prompt to Gemini API");
    const result = await model.generateContent(prompt);
    const response = result.response;
    const textResponse = response.text();
    console.log("Gemini API response:", textResponse);
    // Parse the AI response
    try {
      // Extract JSON if it's within markdown code blocks
      const jsonMatch = textResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/) || textResponse.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : textResponse;
      const parsedResponse = JSON.parse(jsonText.trim());
      console.log("DEBUG: AI Parsed response:", JSON.stringify(parsedResponse, null, 2));
      // Post-process to extract assignee if AI missed it
      if (parsedResponse.action === "addMaintenanceTask" && !parsedResponse.data.assignee) {
        const assigneeMatch = command.match(/assign\s+(?:to|it\s+to)\s+([^,\s]+(?:\s+[^,\s]+)*)/i);
        if (assigneeMatch) {
          parsedResponse.data.assignee = assigneeMatch[1].trim();
          console.log("DEBUG: Post-processed assignee extraction:", parsedResponse.data.assignee);
        }
      }
      // Process the AI's suggested action
      let processingResult;
      switch(parsedResponse.action){
        case "addProperty":
          processingResult = await handleAddProperty(supabase, userId, parsedResponse.data);
          break;
        case "createCollection":
          processingResult = await handleCreateCollection(supabase, userId, parsedResponse.data);
          break;
        case "addMaintenanceTask":
          processingResult = await handleAddMaintenanceTask(supabase, userId, parsedResponse.data);
          break;
        case "addInventoryItem":
          processingResult = await handleAddInventoryItem(supabase, userId, parsedResponse.data);
          break;
        case "updateInventoryItem":
          processingResult = await handleUpdateInventoryItem(supabase, userId, parsedResponse.data);
          break;
        case "createPurchaseOrder":
          processingResult = await handleCreatePurchaseOrder(supabase, userId, parsedResponse.data);
          break;
        default:
          // Generate intelligent suggestions based on the original command
          const intelligentSuggestion = await generateIntelligentSuggestions(
            command,
            supabase,
            userId,
            {
              properties: userProperties,
              inventoryItems: formattedInventoryItems
            }
          );

          processingResult = {
            success: false,
            message: intelligentSuggestion.message,
            suggestions: intelligentSuggestion.suggestions,
            category: intelligentSuggestion.category,
            intent: intelligentSuggestion.intent
          };
      }
      return new Response(JSON.stringify(processingResult), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    } catch (e) {
      console.error("Error parsing Gemini response as JSON:", e);
      console.log("Raw response:", textResponse);
      return new Response(JSON.stringify({
        success: false,
        message: "I had trouble understanding that. Could you rephrase your request?",
        suggestions: [
          "Try being more specific about what you want to do",
          "Include property names, item names, or task descriptions",
          "Use commands like: 'Add', 'Create', 'Update', or 'Fix'"
        ],
        category: 'context'
      }), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    }
  } catch (error) {
    console.error("Error in AI command processor:", error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message || "An error occurred while processing your request"
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  }
};
// Handler functions for different actions
async function handleAddProperty(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name || !data.address || !data.city || !data.state) {
      return {
        success: false,
        message: "Missing required property information. Please provide at least name, address, city, and state."
      };
    }
    // Insert the new property
    const { data: newProperty, error } = await supabase.from("properties").insert({
      user_id: userId,
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip: data.zip || "",
      bedrooms: data.bedrooms || 1,
      bathrooms: data.bathrooms || 1
    }).select().single();
    if (error) {
      console.error("Error adding property:", error);
      return {
        success: false,
        message: `Failed to add property: ${error.message}`
      };
    }
    return {
      success: true,
      message: `Successfully added property "${data.name}"`,
      action: "addProperty",
      entityType: "property",
      entityId: newProperty.id
    };
  } catch (error) {
    console.error("Error in handleAddProperty:", error);
    return {
      success: false,
      message: `Error adding property: ${error.message}`
    };
  }
}
async function handleCreateCollection(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Collection name is required."
      };
    }
    // Insert the new collection
    const { data: newCollection, error } = await supabase.from("collections").insert({
      user_id: userId,
      name: data.name
    }).select().single();
    if (error) {
      console.error("Error creating collection:", error);
      return {
        success: false,
        message: `Failed to create collection: ${error.message}`
      };
    }
    let additionalMessage = "";
    // If budget was specified, update the message
    if (data.budget) {
      additionalMessage = ` with a budget of $${data.budget}`;
    }
    return {
      success: true,
      message: `Successfully created collection "${data.name}"${additionalMessage}`,
      action: "createCollection",
      entityType: "collection",
      entityId: newCollection.id
    };
  } catch (error) {
    console.error("Error in handleCreateCollection:", error);
    return {
      success: false,
      message: `Error creating collection: ${error.message}`
    };
  }
}
async function handleAddMaintenanceTask(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.title) {
      return {
        success: false,
        message: "Maintenance task title is required."
      };
    }
    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = null;
    if (data.property) {
      const { data: propertyData, error: propertyError } = await supabase
        .from("properties")
        .select(`
          id, name,
          team_properties!inner(
            team_id,
            team_members!inner(
              user_id,
              status
            )
          )
        `)
        .eq("team_properties.team_members.user_id", userId)
        .eq("team_properties.team_members.status", "active")
        .ilike("name", `%${data.property}%`)
        .limit(1)
        .single();
      if (propertyError) {
        console.warn("Property not found:", data.property);
        // Get all properties for intelligent suggestions
        const { data: allProperties } = await supabase
          .from("properties")
          .select("name")
          .eq("user_id", userId);

        if (allProperties && allProperties.length > 0) {
          const propertySuggestions = suggestProperties(allProperties);
          return {
            success: false,
            message: `I couldn't find a property named "${data.property}". Did you mean one of these?`,
            suggestions: [
              ...propertySuggestions.map(name => `Try: "Create maintenance task for [description] at ${name}"`),
              `Or add new property: "Add property named ${data.property} at [address]"`,
              `Check spelling: Make sure "${data.property}" is spelled correctly`
            ],
            category: 'data'
          };
        }
      } else if (propertyData) {
        propertyId = propertyData.id;
        propertyName = propertyData.name;
      }
    } else {
      // No property specified - ask user to specify one
      // Get all properties accessible to the user (including team properties)
      const { data: allProperties } = await supabase
        .from("properties")
        .select(`
          name,
          team_properties!inner(
            team_id,
            team_members!inner(
              user_id,
              status
            )
          )
        `)
        .eq("team_properties.team_members.user_id", userId)
        .eq("team_properties.team_members.status", "active");

      if (allProperties && allProperties.length > 0) {
        const propertySuggestions = suggestProperties(allProperties);
        return {
          success: false,
          message: "I need to know which property this maintenance task is for. Which property should I assign this to?",
          suggestions: [
            ...propertySuggestions.map(name => `Try: "${data.title} at ${name}"`),
            "Be specific: 'Create maintenance task for [description] at [property name]'",
            "Example: 'Fix the broken sink at Beach House'"
          ],
          category: 'maintenance',
          intent: 'maintenance_property_required'
        };
      } else {
        return {
          success: false,
          message: "You don't have access to any properties yet. Please contact your property manager or add a property first.",
          suggestions: [
            "Contact your property manager to get access to properties",
            "If you're a property owner: 'Add property named [Name] at [Address] with [X] bedrooms'",
            "Example: 'Add property named Beach House at 123 Ocean Drive, Miami, FL with 3 bedrooms'"
          ],
          category: 'property',
          intent: 'property_required'
        };
      }
    }
    // Get assignee info if assignee name was provided
    let assigneeId = null;
    let assigneeName: string | null = null;
    let providerId = null;
    console.log("DEBUG: Maintenance task data received:", JSON.stringify(data, null, 2));
    if (data.assignee) {
      console.log("DEBUG: Looking for assignee:", data.assignee);
      // Try to find assignee by name (first name, last name, or full name)
      const { data: assigneeData, error: assigneeError } = await supabase.from("profiles").select("id, first_name, last_name, email, role").or(`first_name.ilike.%${data.assignee}%,last_name.ilike.%${data.assignee}%,email.ilike.%${data.assignee}%`).limit(5);
      if (assigneeError) {
        console.warn("Error finding assignee:", assigneeError);
      } else if (assigneeData && assigneeData.length > 0) {
        // If multiple matches, prefer exact matches
        let bestMatch = assigneeData[0];
        // Look for exact first name or last name match
        const exactMatch = assigneeData.find((person)=>person.first_name?.toLowerCase() === data.assignee.toLowerCase() || person.last_name?.toLowerCase() === data.assignee.toLowerCase() || `${person.first_name} ${person.last_name}`.toLowerCase() === data.assignee.toLowerCase());
        if (exactMatch) {
          bestMatch = exactMatch;
        }
        assigneeId = bestMatch.id;
        assigneeName = `${bestMatch.first_name} ${bestMatch.last_name}`;
        // If the assignee is a service provider, also set provider_id
        if (bestMatch.role === 'service_provider') {
          providerId = bestMatch.id;
        }
        console.log(`Found assignee: ${assigneeName} (${bestMatch.email}, role: ${bestMatch.role})`);
      } else {
        console.warn("DEBUG: Assignee not found:", data.assignee);
      }
    } else {
      console.log("DEBUG: No assignee field in data");
    }
    // Insert the new maintenance task
    const { data: newTask, error } = await supabase.from("maintenance_tasks").insert({
      user_id: userId,
      title: data.title,
      description: data.description || "",
      property_id: propertyId,
      property_name: propertyName,
      severity: data.severity || "medium",
      status: "new",
      due_date: data.dueDate || null,
      assigned_to: assigneeName,
      provider_id: providerId
    }).select().single();
    if (error) {
      console.error("Error adding maintenance task:", error);
      return {
        success: false,
        message: `Failed to add maintenance task: ${error.message}`
      };
    }
    // Build success message with assignee info
    let successMessage = `Successfully added maintenance task "${data.title}" for ${propertyName}`;
    if (assigneeName) {
      successMessage += ` and assigned to ${assigneeName}`;
    }
    if (data.dueDate) {
      successMessage += ` (due: ${data.dueDate})`;
    }
    return {
      success: true,
      message: successMessage,
      action: "addMaintenanceTask",
      entityType: "maintenance_task",
      entityId: newTask.id
    };
  } catch (error) {
    console.error("Error in handleAddMaintenanceTask:", error);
    return {
      success: false,
      message: `Error adding maintenance task: ${error.message}`
    };
  }
}
// Smart inventory handler that decides whether to add, update, or copy
async function handleSmartInventoryAdd(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Item name is required. Please specify what item you want to add.",
        suggestions: [
          "Try: 'Add [quantity] [item name] to [property name]'",
          "Example: 'Add 5 towels to Beach House'",
          "Be specific: 'Add 3 bath towels to Ocean View'"
        ],
        category: 'inventory',
        intent: 'missing_item_name'
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = null;
    if (data.property) {
      // First, let's see what properties are available for debugging
      const { data: allUserProperties } = await supabase
        .from("properties")
        .select("id, name")
        .eq("user_id", userId);
      console.log('Available properties for user:', allUserProperties);

      // Try multiple matching strategies for better property matching
      let propertyData = null;
      let propertyError = null;

      // Strategy 1: Exact match (case insensitive)
      console.log(`Looking for property: "${data.property}" for user: ${userId}`);
      const exactMatch = await supabase
        .from("properties")
        .select("id, name")
        .eq("user_id", userId)
        .ilike("name", data.property)
        .limit(1)
        .maybeSingle();

      console.log('Exact match result:', exactMatch);

      if (exactMatch.data) {
        propertyData = exactMatch.data;
      } else {
        // Strategy 2: Partial match (contains the search term)
        console.log(`Trying partial match with pattern: %${data.property}%`);
        const partialMatch = await supabase
          .from("properties")
          .select("id, name")
          .eq("user_id", userId)
          .ilike("name", `%${data.property}%`)
          .limit(1)
          .maybeSingle();

        console.log('Partial match result:', partialMatch);

        if (partialMatch.data) {
          propertyData = partialMatch.data;
          console.log('Found partial match:', propertyData);
        } else {
          // Strategy 3: Match the beginning of the property name (for cases like "Thames" matching "Thames (Andy)")
          console.log(`Trying beginning match with pattern: ${data.property}%`);
          const beginningMatch = await supabase
            .from("properties")
            .select("id, name")
            .eq("user_id", userId)
            .ilike("name", `${data.property}%`)
            .limit(1)
            .maybeSingle();

          console.log('Beginning match result:', beginningMatch);

          if (beginningMatch.data) {
            propertyData = beginningMatch.data;
            console.log('Found beginning match:', propertyData);
          } else {
            // Get all available properties for suggestions (including team properties)
            const { data: allProperties } = await supabase
              .from("properties")
              .select(`
                name,
                team_properties!inner(
                  team_id,
                  team_members!inner(
                    user_id,
                    status
                  )
                )
              `)
              .eq("team_properties.team_members.user_id", userId)
              .eq("team_properties.team_members.status", "active")
              .order("name");

            const propertyNames = allProperties?.map(p => p.name) || [];
            propertyError = {
              message: "Property not found",
              availableProperties: propertyNames
            };
          }
        }
      }

      if (propertyError || !propertyData) {
        // Property not found - suggest available properties (including team properties)
        const { data: allProperties } = await supabase
          .from("properties")
          .select(`
            name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active");

        const propertySuggestions = allProperties?.slice(0, 5).map((p: any) => `"${p.name}"`) || [];

        return {
          success: false,
          message: `Property "${data.property}" not found. Which property did you mean?`,
          suggestions: [
            ...propertySuggestions.map((name: string) => `Try: "Add ${data.quantity || 1} ${data.name} to ${name}"`),
            "Check your property names in the Properties section",
            "Make sure the property name is spelled correctly"
          ],
          category: 'inventory',
          intent: 'property_not_found'
        };
      } else {
        propertyId = propertyData.id;
        propertyName = propertyData.name;
      }
    }

    if (!propertyId) {
      return {
        success: false,
        message: "Please specify which property to add the item to.",
        suggestions: [
          "Try: 'Add [quantity] [item name] to [property name]'",
          "Example: 'Add 5 towels to Beach House'",
          "Specify the property: 'Add items to Ocean View'"
        ],
        category: 'inventory',
        intent: 'missing_property'
      };
    }

    // Check if item already exists in the target property
    const { data: existingItems } = await supabase
      .from("inventory_items")
      .select("*")
      .eq("user_id", userId)
      .eq("property_id", propertyId)
      .ilike("name", `%${data.name}%`);

    // If item exists in target property, update it instead
    if (existingItems && existingItems.length > 0) {
      const existingItem = existingItems[0];
      const updateData = {
        ...data,
        id: existingItem.id,
        increaseQuantity: data.quantity || 1
      };
      return await handleUpdateInventoryItem(supabase, userId, updateData);
    }

    // Check if similar item exists in other properties
    const { data: similarItems } = await supabase
      .from("inventory_items")
      .select("*, properties!inner(name)")
      .eq("user_id", userId)
      .neq("property_id", propertyId)
      .ilike("name", `%${data.name}%`)
      .limit(5);

    // If similar item exists in other properties, offer to copy it
    if (similarItems && similarItems.length > 0) {
      const sourceItem = similarItems[0];
      return await copyInventoryItemToProperty(supabase, userId, sourceItem, propertyId, propertyName, data.quantity || 1);
    }

    // Item doesn't exist anywhere, create new one
    const itemData = {
      ...data,
      property: propertyName,
      propertyId: propertyId
    };
    return await handleAddInventoryItem(supabase, userId, itemData);

  } catch (error) {
    console.error("Error in handleSmartInventoryAdd:", error);
    return {
      success: false,
      message: `Error processing inventory request: ${error.message}`
    };
  }
}

// Copy an existing inventory item from one property to another
async function copyInventoryItemToProperty(supabase, userId, sourceItem, targetPropertyId, targetPropertyName, quantity) {
  try {
    // Get collection ID if collection exists
    let collectionId = null;
    if (sourceItem.collection && sourceItem.collection !== "General") {
      const { data: collectionData } = await supabase
        .from("collections")
        .select("id")
        .eq("user_id", userId)
        .eq("name", sourceItem.collection)
        .limit(1)
        .single();

      if (collectionData) {
        collectionId = collectionData.id;
      }
    }

    // Create new inventory item based on the source item
    const { data: newItem, error } = await supabase
      .from("inventory_items")
      .insert({
        user_id: userId,
        name: sourceItem.name,
        property_id: targetPropertyId,
        quantity: quantity,
        min_quantity: sourceItem.min_quantity || 1,
        collection_id: collectionId,
        collection: sourceItem.collection || "General",
        price: sourceItem.price,
        amazon_url: sourceItem.amazon_url,
        walmart_url: sourceItem.walmart_url,
        target_url: sourceItem.target_url,
        image_url: sourceItem.image_url
      })
      .select()
      .single();

    if (error) {
      console.error("Error copying inventory item:", error);
      return {
        success: false,
        message: `Failed to copy item: ${error.message}`
      };
    }

    const sourcePropertyName = sourceItem.properties?.name || "another property";

    return {
      success: true,
      message: `Successfully copied "${sourceItem.name}" from ${sourcePropertyName} to ${targetPropertyName}. Added ${quantity} items.`,
      action: "copyInventoryItem",
      entityType: "inventory_item",
      entityId: newItem.id,
      suggestions: [
        `Item copied with same settings (collection: ${sourceItem.collection || "General"})`,
        `You can now manage "${sourceItem.name}" separately for each property`,
        `Update quantities independently: "Set ${sourceItem.name} to [number] at ${targetPropertyName}"`
      ]
    };

  } catch (error) {
    console.error("Error in copyInventoryItemToProperty:", error);
    return {
      success: false,
      message: `Error copying inventory item: ${error.message}`
    };
  }
}

async function handleAddInventoryItem(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Inventory item name is required."
      };
    }
    // Get property ID if property name was provided
    let propertyId = null;
    if (data.property) {
      console.log(`[handleAddInventoryItem] Looking for property: "${data.property}" for user: ${userId}`);

      // First, let's see what properties exist for this user (including team properties)
      const { data: allUserProperties, error: allPropsError } = await supabase
        .from("properties")
        .select(`
          id,
          name,
          user_id,
          team_properties!inner(
            team_id,
            team_members!inner(
              user_id,
              status
            )
          )
        `)
        .eq("team_properties.team_members.user_id", userId)
        .eq("team_properties.team_members.status", "active");

      console.log(`[handleAddInventoryItem] All team properties for user ${userId}:`, allUserProperties);
      console.log(`[handleAddInventoryItem] Error fetching team properties:`, allPropsError);

      // Now try the property lookup using team properties with RPC function
      const { data: propertyData, error: propertyError } = await supabase.rpc('get_user_property_by_name', {
        p_user_id: userId,
        p_property_name: data.property
      });

      console.log(`[handleAddInventoryItem] Property lookup result:`, propertyData);
      console.log(`[handleAddInventoryItem] Property lookup error:`, propertyError);

      if (propertyError) {
        console.warn("Property not found:", data.property, propertyError);
      } else if (propertyData && propertyData.length > 0) {
        propertyId = propertyData[0].id;
        console.log(`[handleAddInventoryItem] Found property ID: ${propertyId}`);
      } else {
        console.warn("No property found matching:", data.property);
      }
    }
    if (!propertyId) {
      return {
        success: false,
        message: "Please specify a valid property for the inventory item."
      };
    }
    // Get collection ID if collection name was provided
    let collectionId = null;
    if (data.collection) {
      const { data: collectionData, error: collectionError } = await supabase.from("collections").select("id").eq("user_id", userId).ilike("name", `%${data.collection}%`).limit(1).single();
      if (collectionError) {
        console.warn("Collection not found:", data.collection);
      } else if (collectionData) {
        collectionId = collectionData.id;
      }
    }
    // Check if this exact item already exists at this property
    console.log(`[handleAddInventoryItem] Checking for existing item at property: "${data.name}" at property: ${propertyId}`);

    const { data: existingAtProperty, error: existingError } = await supabase
      .from("inventory_items")
      .select("id, name, quantity")
      .eq("user_id", userId)
      .eq("property_id", propertyId)
      .ilike("name", `%${data.name}%`)
      .limit(1);

    if (existingError) {
      console.error("Error checking existing items at property:", existingError);
    }

    // If item already exists at this property, update quantity
    if (existingAtProperty && existingAtProperty.length > 0) {
      const existingItem = existingAtProperty[0];
      const newQuantity = existingItem.quantity + (data.quantity || 1);

      console.log(`[handleAddInventoryItem] Updating existing item at property:`, existingItem);

      const { data: updatedItem, error: updateError } = await supabase
        .from("inventory_items")
        .update({ quantity: newQuantity })
        .eq("id", existingItem.id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating inventory item:", updateError);
        return {
          success: false,
          message: `Failed to update inventory item: ${updateError.message}`
        };
      }

      return {
        success: true,
        message: `Successfully added ${data.quantity || 1} to existing ${existingItem.name} at this property (new total: ${newQuantity})`,
        action: "updateInventoryItem",
        entityType: "inventory_item",
        entityId: existingItem.id
      };
    }

    // Look for the same item at other properties to copy details
    console.log(`[handleAddInventoryItem] Looking for template item: "${data.name}" at other properties`);

    const { data: templateItems, error: searchError } = await supabase
      .from("inventory_items")
      .select("*")
      .eq("user_id", userId)
      .ilike("name", `%${data.name}%`)
      .limit(5);

    console.log(`[handleAddInventoryItem] Found template items:`, templateItems);

    if (searchError) {
      console.error("Error searching for template items:", searchError);
    }

    // Use details from existing item if available
    let itemDetails = {
      name: data.name,
      min_quantity: data.minQuantity || 1,
      collection: data.collection || "General",
      collection_id: collectionId,
      description: null,
      brand: null,
      model: null,
      purchase_price: null,
      amazon_url: null,
      image_url: null
    };

    if (templateItems && templateItems.length > 0) {
      // Look for exact match first
      const exactMatch = templateItems.find(item => item.name.toLowerCase() === data.name.toLowerCase());
      const templateItem = exactMatch || templateItems[0];

      console.log(`[handleAddInventoryItem] Using template item:`, templateItem);

      // Copy all details from the template item
      itemDetails = {
        name: templateItem.name, // Use exact name from template
        min_quantity: data.minQuantity || templateItem.min_quantity || 1,
        collection: data.collection || templateItem.collection || "General",
        collection_id: collectionId || templateItem.collection_id,
        description: templateItem.description,
        brand: templateItem.brand,
        model: templateItem.model,
        purchase_price: templateItem.purchase_price,
        amazon_url: templateItem.amazon_url,
        image_url: templateItem.image_url
      };
    }

    // Create a new inventory item for this property using template details
    console.log(`[handleAddInventoryItem] Creating new inventory item for property: ${propertyId}`, itemDetails);
    const { data: newItem, error } = await supabase.from("inventory_items").insert({
      user_id: userId,
      name: itemDetails.name,
      property_id: propertyId,
      quantity: data.quantity || 1,
      min_quantity: itemDetails.min_quantity,
      collection_id: itemDetails.collection_id,
      collection: itemDetails.collection,
      description: itemDetails.description,
      brand: itemDetails.brand,
      model: itemDetails.model,
      purchase_price: itemDetails.purchase_price,
      amazon_url: itemDetails.amazon_url,
      image_url: itemDetails.image_url
    }).select().single();

    if (error) {
      console.error("Error adding inventory item:", error);
      return {
        success: false,
        message: `Failed to add inventory item: ${error.message}`
      };
    }

    return {
      success: true,
      message: `Successfully added ${data.quantity || 1} ${data.name} to inventory`,
      action: "addInventoryItem",
      entityType: "inventory_item",
      entityId: newItem.id
    };
  } catch (error) {
    console.error("Error in handleAddInventoryItem:", error);
    return {
      success: false,
      message: `Error adding inventory item: ${error.message}`
    };
  }
}
async function handleUpdateInventoryItem(supabase, userId, data) {
  try {
    if (!data.name && !data.id) {
      return {
        success: false,
        message: "Item name or ID is required to update an inventory item."
      };
    }
    // Find the item by name or ID
    let query = supabase.from("inventory_items").select("*, properties(name)").eq("user_id", userId);
    if (data.id) {
      query = query.eq("id", data.id);
    } else if (data.name) {
      // Try to find a close match for the item name
      // First try with exact match
      const { data: exactItems } = await query.ilike("name", data.name).limit(5);
      if (exactItems && exactItems.length > 0) {
        // Found exact matches
        console.log("Found exact matches for inventory item:", exactItems.length);
        // If property is specified, try to find a match for that property
        if (data.property) {
          const propertyMatch = exactItems.find((item) => {
            if (!item.properties || !item.properties.name) return false;
            const propertyName = item.properties.name.toLowerCase();
            const searchProperty = data.property.toLowerCase();

            // Try multiple matching strategies
            return propertyName === searchProperty || // Exact match
                   propertyName.includes(searchProperty) || // Contains search term
                   propertyName.startsWith(searchProperty); // Starts with search term (for "Thames" matching "Thames (Andy)")
          });

          if (propertyMatch) {
            console.log("Found property match for inventory item:", propertyMatch.id);
            return await updateInventoryItemQuantity(supabase, propertyMatch, data);
          }
        }
        // If no property match or no property specified, use the first exact match
        return await updateInventoryItemQuantity(supabase, exactItems[0], data);
      }
      // Try with partial match if no exact match found
      const { data: items, error: findError } = await query.ilike("name", `%${data.name}%`).limit(5);
      if (findError) {
        console.error("Error finding inventory item:", findError);
        return {
          success: false,
          message: `Error finding inventory item: ${findError.message}`
        };
      }
      if (!items || items.length === 0) {
        // Get all inventory items for intelligent suggestions
        const { data: allInventoryItems } = await supabase
          .from("inventory_items")
          .select("name, properties(name)")
          .eq("user_id", userId);

        const similarItems = findSimilarItems(data.name, allInventoryItems || []);

        if (similarItems.length > 0) {
          return {
            success: false,
            message: `I didn't find any "${data.name}" in your inventory, but I found some similar items:`,
            suggestions: similarItems.map(item => `Did you mean ${item}?`),
            category: 'data'
          };
        } else {
          return {
            success: false,
            message: `I didn't find "${data.name}" in your inventory. Let me help you add it or find what you're looking for.`,
            suggestions: [
              `Add "${data.name}" to inventory: "Add [quantity] ${data.name} to [property name]"`,
              `Check spelling: Make sure "${data.name}" is spelled correctly`,
              `Browse inventory: "Show me all inventory items" to see what's available`,
              `Be more specific: "Update [exact item name] at [property name]"`
            ],
            category: 'data'
          };
        }
      }
      // If property is specified, try to find a match for that property
      if (data.property && items.length > 1) {
        const propertyMatch = items.find((item)=>item.properties && item.properties.name && item.properties.name.toLowerCase().includes(data.property.toLowerCase()));
        if (propertyMatch) {
          console.log("Found property match for inventory item:", propertyMatch.id);
          return await updateInventoryItemQuantity(supabase, propertyMatch, data);
        }
      }
      // Use the first match if no property match or no property specified
      return await updateInventoryItemQuantity(supabase, items[0], data);
    }
    // If we get here, we're using ID-based lookup
    const { data: items, error: findError } = await query.limit(1);
    if (findError) {
      console.error("Error finding inventory item:", findError);
      return {
        success: false,
        message: `Error finding inventory item: ${findError.message}`
      };
    }
    if (!items || items.length === 0) {
      // Try to find similar items to suggest
      const { data: similarItems } = await supabase
        .from("inventory_items")
        .select("name, properties(name)")
        .eq("user_id", userId)
        .limit(5);

      let suggestions = [];
      if (similarItems && similarItems.length > 0 && data.name) {
        // Find items with similar names
        const searchTerm = data.name.toLowerCase();
        const similar = similarItems.filter(item =>
          item.name.toLowerCase().includes(searchTerm.split(' ')[0]) ||
          searchTerm.includes(item.name.toLowerCase().split(' ')[0])
        ).slice(0, 3);

        if (similar.length > 0) {
          suggestions = similar.map(item =>
            `Did you mean "${item.name}"${item.properties?.name ? ` at ${item.properties.name}` : ''}?`
          );
        }
      }

      // Enhanced suggestions for inventory not found
      const enhancedSuggestions = suggestions.length > 0 ? suggestions : [
        `No "${data.name || data.id}" found in inventory. Click here to go to the inventory dashboard to add some manually or via Amazon.`,
        `Quick add: "Add [quantity] ${data.name || data.id} to [property name]"`,
        `Order from Amazon: "Order ${data.name || data.id} from Amazon for [property name]"`,
        `Browse inventory: Check your existing items for alternatives`
      ];

      return {
        success: false,
        message: `No "${data.name || data.id}" found in inventory. Click here to go to the inventory dashboard to add some manually or via Amazon.`,
        suggestions: enhancedSuggestions,
        category: 'inventory',
        intent: 'item_not_found'
      };
    }
    // Update the item
    return await updateInventoryItemQuantity(supabase, items[0], data);
  } catch (error) {
    console.error("Error in handleUpdateInventoryItem:", error);
    return {
      success: false,
      message: `Error updating inventory item: ${error.message}`
    };
  }
}
// Helper function to update inventory item quantity
async function updateInventoryItemQuantity(supabase, item, data) {
  try {
    const updates: { quantity?: number; min_quantity?: number } = {};
    const originalQuantity = item.quantity || 0;
    const originalMinQuantity = item.min_quantity || 0;
    let quantityChanged = false;
    let minQuantityChanged = false;
    let quantityChangeDescription = "";
    // Handle different quantity update scenarios
    if (data.quantity !== undefined) {
      // Direct quantity setting
      updates.quantity = data.quantity;
      quantityChanged = true;
      quantityChangeDescription = `set to ${data.quantity}`;
    } else if (data.increaseQuantity !== undefined) {
      // Explicit increase
      updates.quantity = originalQuantity + data.increaseQuantity;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${data.increaseQuantity} to ${updates.quantity}`;
    } else if (data.decreaseQuantity !== undefined) {
      // Explicit decrease
      updates.quantity = Math.max(0, originalQuantity - data.decreaseQuantity);
      quantityChanged = true;
      quantityChangeDescription = `decreased by ${data.decreaseQuantity} to ${updates.quantity}`;
    } else if (data.addMore !== undefined) {
      // "Add more" scenario
      updates.quantity = originalQuantity + data.addMore;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${data.addMore} to ${updates.quantity}`;
    } else if (data.setMinimum !== undefined) {
      // If user wants to ensure a minimum level
      const targetMinimum = data.setMinimum;
      if (originalQuantity < targetMinimum) {
        updates.quantity = targetMinimum;
        quantityChanged = true;
        quantityChangeDescription = `increased to minimum level of ${targetMinimum}`;
      }
    } else if (data.isLowStock === true) {
      // Generic "low stock" or "need more" scenario
      // Increase by a reasonable default amount based on item type
      const defaultIncrease = Math.max(5, originalMinQuantity * 2);
      updates.quantity = originalQuantity + defaultIncrease;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${defaultIncrease} to ${updates.quantity} (default restock amount)`;
    }
    // Handle minimum quantity updates
    if (data.minQuantity !== undefined) {
      updates.min_quantity = data.minQuantity;
      minQuantityChanged = true;
    }
    if (data.min_quantity !== undefined) {
      updates.min_quantity = data.min_quantity;
      minQuantityChanged = true;
    }
    // If setting a minimum level that's also higher than current min_quantity, update that too
    if (data.setMinimum !== undefined && data.setMinimum > originalMinQuantity) {
      updates.min_quantity = data.setMinimum;
      minQuantityChanged = true;
    }
    if (Object.keys(updates).length === 0) {
      return {
        success: false,
        message: "No updates provided for the inventory item."
      };
    }
    // Update the item
    const { data: _updatedItem, error: updateError } = await supabase.from("inventory_items").update(updates).eq("id", item.id).select().single();
    if (updateError) {
      console.error("Error updating inventory item:", updateError);
      return {
        success: false,
        message: `Failed to update inventory item: ${updateError.message}`
      };
    }
    // Build a descriptive message about what was updated
    let updateMessage = "";
    if (quantityChanged) {
      updateMessage += `quantity ${quantityChangeDescription}`;
    }
    if (minQuantityChanged) {
      if (updateMessage) updateMessage += " and ";
      updateMessage += `minimum quantity set to ${updates.min_quantity}`;
    }
    // Include property name in the message if available
    const propertyName = item.properties?.name ? ` at ${item.properties.name}` : "";
    return {
      success: true,
      message: `Successfully updated ${item.name}${propertyName}: ${updateMessage}`,
      action: "updateInventoryItem",
      entityType: "inventory_item",
      entityId: item.id
    };
  } catch (error) {
    console.error("Error in updateInventoryItemQuantity:", error);
    return {
      success: false,
      message: `Error updating inventory item: ${error.message}`
    };
  }
}
async function handleCreatePurchaseOrder(supabase, userId, data) {
  try {
    // Get items to include in the purchase order
    let itemsToInclude: any[] = [];
    if (data.allLowStock) {
      // Include all low stock items using RPC function
      let propertyId = null;
      // Filter by property if specified
      if (data.property) {
        // First find the property ID by name
        const { data: properties, error: propError } = await supabase
          .from("properties")
          .select(`
            id, name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active")
          .ilike("name", `%${data.property}%`)
          .limit(1);
        if (propError) {
          console.error("Error finding property:", propError);
        } else if (properties && properties.length > 0) {
          propertyId = properties[0].id;
        }
      }
      // Use RPC function to get low stock items
      const { data: lowStockItems, error: itemsError } = await supabase.rpc('get_low_stock_items', {
        user_id_param: userId,
        property_id_param: propertyId
      });
      if (itemsError) {
        console.error("Error fetching low stock items:", itemsError);
        return {
          success: false,
          message: `Error fetching low stock items: ${itemsError.message}`
        };
      }
      console.log(`Found ${lowStockItems?.length || 0} low stock items for user ${userId}${propertyId ? ` at property ${propertyId}` : ''}`);
      if (!lowStockItems || lowStockItems.length === 0) {
        return {
          success: false,
          message: `No low stock items found${data.property ? ` at property "${data.property}"` : ''}. Check your inventory to make sure items have minimum quantities set.`
        };
      }
      itemsToInclude = (lowStockItems || []).map((item)=>({
          inventory_item_id: item.id,
          item_name: item.name,
          quantity: Math.max(item.min_quantity - item.quantity, 1),
          price: item.price,
          amazon_url: item.amazon_url,
          walmart_url: item.walmart_url,
          property_id: item.property_id,
          property_name: item.property_name
        }));
    } else if (data.items && data.items.length > 0) {
      // Include specific items or categories
      for (const itemData of data.items){
        // Find items by name or category
        let query = supabase.from("inventory_items").select(`
            id, name, quantity, min_quantity, property_id, collection,
            price, amazon_url, walmart_url,
            properties:property_id (id, name)
          `).eq("user_id", userId);
        // Check if this is a category search (like "cleaning supplies", "bathroom supplies")
        const categoryKeywords = {
          'cleaning': [
            'cleaner',
            'soap',
            'detergent',
            'disinfectant',
            'bleach',
            'spray'
          ],
          'bathroom': [
            'toilet',
            'towel',
            'shampoo',
            'soap',
            'tissue'
          ],
          'kitchen': [
            'dish',
            'plate',
            'cup',
            'glass',
            'utensil',
            'knife',
            'fork'
          ],
          'bedroom': [
            'sheet',
            'pillow',
            'blanket',
            'towel'
          ],
          'laundry': [
            'detergent',
            'softener',
            'bleach'
          ]
        };
        let foundItems: any[] = [];
        let isCategory = false;
        // Check if the item name matches a category
        for (const [category, keywords] of Object.entries(categoryKeywords)){
          if (itemData.name.toLowerCase().includes(category)) {
            isCategory = true;
            // Search for items that match any of the category keywords
            for (const keyword of keywords){
              const { data: categoryItems, error: categoryError } = await query.ilike("name", `%${keyword}%`);
              if (!categoryError && categoryItems) {
                foundItems.push(...categoryItems);
              }
            }
            break;
          }
        }
        // If not a category, search by exact name
        if (!isCategory) {
          const { data: exactItems, error: findError } = await query.ilike("name", `%${itemData.name}%`);
          if (!findError && exactItems) {
            foundItems = exactItems;
          }
        }
        // Remove duplicates from foundItems
        const uniqueItems = foundItems.filter((item, index, self)=>index === self.findIndex((i)=>i.id === item.id));
        if (uniqueItems && uniqueItems.length > 0) {
          for (const item of uniqueItems){
            itemsToInclude.push({
              inventory_item_id: item.id,
              item_name: item.name,
              quantity: itemData.quantity || Math.max(item.min_quantity - item.quantity, 1),
              price: item.price,
              amazon_url: item.amazon_url,
              walmart_url: item.walmart_url,
              property_id: item.property_id,
              property_name: item.properties?.name
            });
          }
        }
      }
    } else {
      return {
        success: false,
        message: "No items specified for the purchase order."
      };
    }
    if (itemsToInclude.length === 0) {
      return {
        success: false,
        message: "No valid items found to include in the purchase order."
      };
    }
    // Group items by property
    const itemsByProperty = {};
    for (const item of itemsToInclude){
      const propertyId = item.property_id;
      if (!itemsByProperty[propertyId]) {
        itemsByProperty[propertyId] = [];
      }
      itemsByProperty[propertyId].push(item);
    }
    const createdOrders: Array<{ id: any; propertyName: any; itemCount: number }> = [];
    // Create purchase orders for each property
    for (const [propertyId, items] of Object.entries(itemsByProperty)){
      const typedItems = items as any[];
      // Calculate total price
      const totalPrice = typedItems.reduce((sum, item)=>sum + (item.price || 0) * item.quantity, 0);
      // Create the purchase order
      const { data: order, error: orderError } = await supabase.from("purchase_orders").insert({
        user_id: userId,
        property_id: propertyId,
        status: "pending",
        total_price: totalPrice > 0 ? totalPrice : null,
        notes: data.notes || ""
      }).select().single();
      if (orderError) {
        console.error("Error creating purchase order:", orderError);
        continue;
      }

      // Add items to the purchase order
      const orderItems = typedItems.map((item)=>({
          purchase_order_id: order.id,
          inventory_item_id: item.inventory_item_id,
          item_name: item.item_name,
          quantity: item.quantity,
          price: item.price,
          amazon_url: item.amazon_url,
          walmart_url: item.walmart_url
        }));

      const { error: itemsError } = await supabase.from("purchase_order_items").insert(orderItems);
      if (itemsError) {
        console.error("Error adding items to purchase order:", itemsError);
        continue;
      }

      createdOrders.push({
        id: order.id,
        propertyName: typedItems[0].property_name,
        itemCount: typedItems.length
      });
    }

    if (createdOrders.length === 0) {
      return {
        success: false,
        message: "Failed to create any purchase orders."
      };
    }
    let message = "";
    if (createdOrders.length === 1) {
      const order = createdOrders[0];
      message = `Successfully created a purchase order with ${order.itemCount} items for ${order.propertyName}`;
    } else {
      message = `Successfully created ${createdOrders.length} purchase orders for different properties`;
    }
    return {
      success: true,
      message,
      action: "createPurchaseOrder",
      entityType: "purchase_order",
      entityId: createdOrders[0].id
    };
  } catch (error) {
    console.error("Error in handleCreatePurchaseOrder:", error);
    return {
      success: false,
      message: `Error creating purchase order: ${error.message}`
    };
  }
}
serve(handler);
