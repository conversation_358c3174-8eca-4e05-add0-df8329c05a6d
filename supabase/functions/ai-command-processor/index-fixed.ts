// Fixed version of handleAddMaintenanceTask function for AI command processor
// This fixes the missing team_id field issue for service providers

async function handleAddMaintenanceTask(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.title) {
      return {
        success: false,
        message: "Maintenance task title is required."
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = null;
    let teamId = null;
    
    if (data.property) {
      // Use RPC function for better property access control
      const { data: propertyData, error: propertyError } = await supabase.rpc('get_user_property_by_name', {
        p_user_id: userId,
        p_property_name: data.property
      });
      
      if (propertyError || !propertyData || propertyData.length === 0) {
        console.warn("Property not found:", data.property);
        // Get all properties for intelligent suggestions
        const { data: allProperties } = await supabase
          .from("properties")
          .select(`
            name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active");

        if (allProperties && allProperties.length > 0) {
          const propertySuggestions = suggestProperties(allProperties);
          return {
            success: false,
            message: "I need to know which property this maintenance task is for. Which property should I assign this to?",
            suggestions: [
              ...propertySuggestions.map(name => `Try: "${data.title} at ${name}"`),
              "Be specific: 'Create maintenance task for [description] at [property name]'",
              "Example: 'Fix the broken sink at Beach House'"
            ],
            category: 'maintenance',
            intent: 'maintenance_property_required'
          };
        } else {
          return {
            success: false,
            message: "No properties found. Please add a property first before creating maintenance tasks.",
            suggestions: [
              "Add a property first: 'Add property [name] at [address]'",
              "Example: 'Add property Beach House at 123 Ocean Drive'"
            ],
            category: 'maintenance',
            intent: 'no_properties_available'
          };
        }
      } else {
        propertyId = propertyData[0].id;
        propertyName = propertyData[0].name;
        
        // Get team_id for this property and user
        const { data: teamPropertyData, error: teamPropertyError } = await supabase
          .from('team_properties')
          .select('team_id, team_members!inner(user_id, status)')
          .eq('property_id', propertyId)
          .eq('team_members.user_id', userId)
          .eq('team_members.status', 'active')
          .limit(1);
          
        if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
          teamId = teamPropertyData[0].team_id;
          console.log(`[handleAddMaintenanceTask] Found team_id ${teamId} for property ${propertyId}`);
        }
      }
    }

    // Get assignee info if assignee name was provided
    let assigneeId = null;
    let assigneeName: string | null = null;
    let providerId = null;
    console.log("DEBUG: Maintenance task data received:", JSON.stringify(data, null, 2));
    
    if (data.assignee) {
      console.log("DEBUG: Looking for assignee:", data.assignee);
      // Try to find assignee by name (first name, last name, or full name)
      const { data: assigneeData, error: assigneeError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name, email, role")
        .or(`first_name.ilike.%${data.assignee}%,last_name.ilike.%${data.assignee}%,email.ilike.%${data.assignee}%`)
        .limit(5);
        
      if (assigneeError) {
        console.warn("Error finding assignee:", assigneeError);
      } else if (assigneeData && assigneeData.length > 0) {
        const assignee = assigneeData[0];
        assigneeId = assignee.id;
        assigneeName = `${assignee.first_name || ''} ${assignee.last_name || ''}`.trim() || assignee.email;
        
        // If assignee is a service provider, set provider_id
        if (assignee.role === 'service_provider') {
          providerId = assignee.id;
        }
        
        console.log("DEBUG: Found assignee:", { assigneeId, assigneeName, providerId });
      } else {
        // If no user found, just use the assignee name as provided
        assigneeName = data.assignee;
        console.log("DEBUG: No user found for assignee, using name as provided:", assigneeName);
      }
    }

    // Insert the new maintenance task with team_id
    const { data: newTask, error } = await supabase.from("maintenance_tasks").insert({
      user_id: userId,
      title: data.title,
      description: data.description || "",
      property_id: propertyId,
      property_name: propertyName || "General",
      severity: data.severity || "medium",
      status: "new",
      due_date: data.dueDate || null,
      assigned_to: assigneeName,
      provider_id: providerId,
      team_id: teamId  // This was missing in the original code!
    }).select().single();
    
    if (error) {
      console.error("Error adding maintenance task:", error);
      return {
        success: false,
        message: `Failed to add maintenance task: ${error.message}`
      };
    }
    
    // Build success message with assignee info
    let successMessage = `Successfully added maintenance task "${data.title}" for ${propertyName || "General"}`;
    if (assigneeName) {
      successMessage += ` and assigned to ${assigneeName}`;
    }
    if (data.dueDate) {
      successMessage += ` (due: ${data.dueDate})`;
    }
    
    return {
      success: true,
      message: successMessage,
      action: "addMaintenanceTask",
      entityType: "maintenance_task",
      entityId: newTask.id
    };
  } catch (error) {
    console.error("Error in handleAddMaintenanceTask:", error);
    return {
      success: false,
      message: `Error adding maintenance task: ${error.message}`
    };
  }
}

// Helper function to suggest properties (if not already defined)
function suggestProperties(properties) {
  return properties.map(p => p.name).slice(0, 5);
}
