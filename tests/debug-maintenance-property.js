// Debug maintenance property assignment issue
const fetch = globalThis.fetch || require('node-fetch');

const SUPABASE_URL = 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAyMDQ4NzQsImV4cCI6MjAzNTc4MDg3NH0.lJF_qhHGBtmXVBaJly_PbCqKdyJgHqSdvQeWbgJhE_s';

async function testMaintenanceCommand() {
  try {
    console.log('🧪 Testing maintenance command without property...');
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-command-processor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        command: 'Fix something',
        userId: 'test-user-id'
      })
    });

    const result = await response.json();
    console.log('📋 Response:', JSON.stringify(result, null, 2));
    
    if (result.success === false && result.category === 'maintenance' && result.intent === 'maintenance_property_required') {
      console.log('✅ FIXED: Maintenance task properly asks for property!');
    } else if (result.success === true) {
      console.log('❌ ISSUE: Maintenance task was created without asking for property');
    } else {
      console.log('❓ UNCLEAR: Unexpected response format');
    }
    
  } catch (error) {
    console.error('❌ Error testing maintenance command:', error);
  }
}

testMaintenanceCommand();
