// Live AI Testing with Real Suggestions
// Tests the AI with actual suggestion examples and generates new ones

console.log('🧪 Testing AI with Suggestion Examples + Generated Tests');

// Test cases from our suggestion examples
const suggestionExamples = [
  // Property Management
  "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
  "Update the Beach House property to have 4 bedrooms instead of 3",
  "Create property Sunset Villa with 2 bathrooms",
  
  // Maintenance Tasks
  "Create a maintenance task to fix the broken sink at Beach House",
  "Fix the AC unit at Ocean View",
  "Repair the leaky faucet in unit 2B",
  "Schedule HVAC inspection for all properties next week",
  "The dishwasher at Ocean View needs repair",
  
  // Inventory Management
  "We're down to only 2 bath towels, we need a minimum of 12",
  "Add 5 more wine glasses to inventory",
  "Update toilet paper quantity to 20 at Beach House",
  "Add 10 towels to the Beach House bathroom collection",
  "We need more cleaning supplies for the bathroom",
  
  // Damage Reports
  "Create a damage report at Beach House for a wine stain on the living room carpet",
  "Update the carpet damage at beach house status to resolved",
  "Report broken window at unit 3A",
  "Document water damage in the basement",
  "Create damage report for scratched hardwood floors",
  
  // Purchase Orders
  "Create a purchase order for all low stock items",
  "Order more towels and bed sheets",
  "Buy new coffee machine for the kitchen",
  "Purchase cleaning supplies for spring cleaning",
  
  // Collections
  "Create a new kitchen collection with a budget of $500",
  "Add items to the luxury amenities collection",
  "Create new bathroom collection with $300 budget",
  
  // Staff/Assignment
  "Assign the plumbing repair to Mike Johnson",
  "Fix the broken AC at Beach House, assign to maintenance team",
  "Schedule team meeting for next Monday",
  
  // Emergency/Urgent
  "Emergency: water leak at Ocean View",
  "Urgent repair needed for broken lock",
  "Guest complaint about heating not working",
  
  // Unclear/Problematic Commands (should trigger intelligent suggestions)
  "Fix something",
  "We need more soap", // Should suggest similar items
  "Add towels to Nonexistent Property", // Should suggest available properties
  "Do maintenance stuff",
  "Order supplies",
  "Something is broken at the house",
  "Update inventory",
  "Create a task",
  "Help with property management",
  "I need assistance with cleaning"
];

// Additional generated test cases
const generatedTestCases = [
  // Edge cases that should trigger intelligent responses
  "soap", // Single word - should ask for clarification
  "broken", // Vague maintenance request
  "towels", // Item without context
  "Beach House", // Property name without action
  "urgent", // Priority without task
  "assign", // Assignment without details
  "order", // Purchase without specifics
  "damage", // Damage without details
  "inventory low", // Stock issue without specifics
  "need help", // Generic help request
  
  // Partial commands that need completion
  "Add property", // Missing details
  "Fix the", // Incomplete maintenance
  "We're out of", // Incomplete inventory
  "Create damage", // Incomplete damage report
  "Buy more", // Incomplete purchase
  "Assign to", // Incomplete assignment
  "Emergency at", // Incomplete emergency
  "Update the", // Incomplete update
  "Schedule", // Incomplete scheduling
  "Report", // Incomplete reporting
  
  // Misspelled or unclear references
  "Fix the AC at Beech House", // Misspelled property
  "Add towls to inventory", // Misspelled item
  "Maintainance needed", // Misspelled action
  "Creat damage report", // Misspelled command
  "Asign to Mike", // Misspelled assignment
  
  // Complex multi-part commands
  "Fix the broken sink at Beach House and assign to Mike Johnson with high priority",
  "We're low on towels, toilet paper, and cleaning supplies at all properties",
  "Create maintenance tasks for HVAC inspection at Beach House and Ocean View",
  "Order replacement parts for dishwasher and schedule repair at Beach House",
  "Document carpet damage and generate invoice for Beach House repair"
];

// Combine all test cases
const allTestCases = [...suggestionExamples, ...generatedTestCases];

console.log(`\n📊 Testing ${allTestCases.length} AI Commands:\n`);

// Simulate AI response analysis
const analyzeAIResponse = (command) => {
  const lowerCommand = command.toLowerCase();
  
  // Check for maintenance task property assignment issue
  if ((lowerCommand.includes('fix') || lowerCommand.includes('repair') || lowerCommand.includes('maintenance')) 
      && !lowerCommand.includes(' at ') && !lowerCommand.includes(' in ')) {
    return {
      issue: 'MAINTENANCE_NO_PROPERTY',
      expected: 'Should ask which property to assign maintenance task to',
      severity: 'HIGH'
    };
  }
  
  // Check for vague commands that should trigger intelligent suggestions
  const vaguePhrases = ['something', 'stuff', 'help', 'need', 'broken', 'fix', 'order', 'add', 'create'];
  const isVague = vaguePhrases.some(phrase => 
    lowerCommand === phrase || 
    (lowerCommand.includes(phrase) && lowerCommand.split(' ').length <= 3)
  );
  
  if (isVague) {
    return {
      issue: 'VAGUE_COMMAND',
      expected: 'Should provide intelligent follow-up questions',
      severity: 'MEDIUM'
    };
  }
  
  // Check for item/property references that might not exist
  if (lowerCommand.includes('soap') && !lowerCommand.includes('dawn')) {
    return {
      issue: 'ITEM_NOT_FOUND',
      expected: 'Should suggest similar items like Dawn professional pot and pan detergent',
      severity: 'MEDIUM'
    };
  }
  
  if (lowerCommand.includes('nonexistent') || lowerCommand.includes('beech house')) {
    return {
      issue: 'PROPERTY_NOT_FOUND',
      expected: 'Should suggest available properties',
      severity: 'MEDIUM'
    };
  }
  
  // Check for successful commands
  const hasSpecificAction = lowerCommand.includes('add') || lowerCommand.includes('create') || 
                           lowerCommand.includes('update') || lowerCommand.includes('fix');
  const hasContext = lowerCommand.includes('beach house') || lowerCommand.includes('ocean view') ||
                    lowerCommand.includes('quantity') || lowerCommand.includes('assign to');
  
  if (hasSpecificAction && hasContext) {
    return {
      issue: 'NONE',
      expected: 'Should process successfully',
      severity: 'NONE'
    };
  }
  
  return {
    issue: 'UNCLEAR',
    expected: 'Should provide contextual guidance',
    severity: 'LOW'
  };
};

// Analyze all test cases
let issueCount = {
  MAINTENANCE_NO_PROPERTY: 0,
  VAGUE_COMMAND: 0,
  ITEM_NOT_FOUND: 0,
  PROPERTY_NOT_FOUND: 0,
  UNCLEAR: 0,
  NONE: 0
};

let highSeverityIssues = [];
let mediumSeverityIssues = [];

allTestCases.forEach((command, index) => {
  const analysis = analyzeAIResponse(command);
  issueCount[analysis.issue]++;
  
  if (analysis.severity === 'HIGH') {
    highSeverityIssues.push({ command, analysis });
  } else if (analysis.severity === 'MEDIUM') {
    mediumSeverityIssues.push({ command, analysis });
  }
  
  // Log first few examples of each issue type
  if (issueCount[analysis.issue] <= 3 && analysis.issue !== 'NONE') {
    console.log(`📍 ${analysis.issue}: "${command}"`);
    console.log(`   Expected: ${analysis.expected}`);
    console.log(`   Severity: ${analysis.severity}\n`);
  }
});

console.log('🎯 Test Analysis Summary:');
console.log(`Total Commands Tested: ${allTestCases.length}`);
console.log(`Successful Commands: ${issueCount.NONE}`);
console.log(`High Severity Issues: ${highSeverityIssues.length}`);
console.log(`Medium Severity Issues: ${mediumSeverityIssues.length}`);
console.log(`Low Severity Issues: ${issueCount.UNCLEAR}`);

console.log('\n🚨 Critical Issues Found:');
console.log(`1. MAINTENANCE_NO_PROPERTY: ${issueCount.MAINTENANCE_NO_PROPERTY} commands`);
console.log('   - Maintenance tasks being assigned to "general" instead of asking for property');
console.log('   - Examples: "Fix something", "Repair needed", "Maintenance task"');

console.log(`\n2. VAGUE_COMMAND: ${issueCount.VAGUE_COMMAND} commands`);
console.log('   - Commands that should trigger intelligent follow-up questions');
console.log('   - Examples: "soap", "broken", "help"');

console.log(`\n3. ITEM_NOT_FOUND: ${issueCount.ITEM_NOT_FOUND} commands`);
console.log('   - Should suggest similar items when exact matches not found');
console.log('   - Examples: "We need more soap"');

console.log('\n🔧 Recommended Fixes:');
console.log('1. Update maintenance task handler to always ask for property when not specified');
console.log('2. Enhance service provider permissions for maintenance task editing/deletion');
console.log('3. Improve fuzzy matching for inventory items and properties');
console.log('4. Add more intelligent follow-up questions for vague commands');

// Export for browser testing
if (typeof window !== 'undefined') {
  window.testAICommands = allTestCases;
  window.analyzeCommand = analyzeAIResponse;
}
