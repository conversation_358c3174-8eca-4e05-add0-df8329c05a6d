// Real AI Testing for Maintenance Property Assignment Fix
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAyMDQ4NzQsImV4cCI6MjAzNTc4MDg3NH0.lJF_qhHGBtmXVBaJly_PbCqKdyJgHqSdvQeWbgJhE_s';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testMaintenancePropertyFix() {
  console.log('🧪 Testing Real AI Maintenance Property Assignment Fix\n');
  
  // Test commands that should ask for property
  const testCommands = [
    'Fix something',
    'Repair needed',
    'Urgent repair needed for broken lock',
    'Assign the plumbing repair to <PERSON>',
    'Create maintenance task for broken sink'
  ];
  
  let fixedCount = 0;
  let totalTests = testCommands.length;
  
  for (const command of testCommands) {
    try {
      console.log(`🔧 Testing: "${command}"`);
      
      const { data, error } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command: command,
          userId: 'test-user-id'
        }
      });
      
      if (error) {
        console.log(`❌ Error: ${error.message}\n`);
        continue;
      }
      
      // Check if the AI properly asks for property
      if (data.success === false && 
          data.category === 'maintenance' && 
          data.intent === 'maintenance_property_required' &&
          data.message.includes('which property')) {
        console.log('✅ FIXED: AI properly asks for property specification');
        console.log(`   Message: "${data.message}"`);
        if (data.suggestions && data.suggestions.length > 0) {
          console.log(`   Suggestions: ${data.suggestions.slice(0, 2).join(', ')}...`);
        }
        fixedCount++;
      } else if (data.success === true) {
        console.log('❌ ISSUE: AI created task without asking for property');
        console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
      } else {
        console.log('❓ UNCLEAR: Unexpected response');
        console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
      }
      
      console.log('');
      
    } catch (error) {
      console.log(`❌ Error testing "${command}": ${error.message}\n`);
    }
  }
  
  console.log('📊 Test Results:');
  console.log(`Fixed Commands: ${fixedCount}/${totalTests}`);
  console.log(`Success Rate: ${Math.round((fixedCount / totalTests) * 100)}%`);
  
  if (fixedCount === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Maintenance property assignment fix is working!');
  } else {
    console.log('⚠️  Some tests failed. The fix may need additional work.');
  }
}

testMaintenancePropertyFix().catch(console.error);
