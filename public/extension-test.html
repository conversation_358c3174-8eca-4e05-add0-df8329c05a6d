<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StayFu Extension Dynamic Detection Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 40px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-family: monospace; 
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 StayFu Extension Dynamic Detection Test</h1>
        <p>This page tests the new dynamic extension detection system that doesn't require hardcoded extension IDs.</p>
        
        <div class="status info">
            <strong>How it works:</strong><br>
            1. Web app requests extension to announce itself<br>
            2. Extension responds with its actual ID<br>
            3. Web app uses that ID for future communication<br>
            4. No more hardcoded extension IDs needed! 🎉
        </div>

        <button onclick="testExtensionDetection()">🚀 Test Extension Detection</button>
        <button onclick="testExtensionSearch()">🔍 Test Extension Search</button>
        <button onclick="clearLog()">🧹 Clear Log</button>

        <div id="status"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let detectedExtensionId = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').innerHTML = '';
        }

        // Listen for extension announcements
        window.addEventListener('message', (event) => {
            if (event.data.type === 'STAYFU_EXTENSION_ANNOUNCEMENT') {
                const info = event.data;
                detectedExtensionId = info.extensionId;
                
                log(`✅ Extension announced itself!`);
                log(`   Extension ID: ${info.extensionId}`);
                log(`   Version: ${info.version || 'Unknown'}`);
                log(`   Connected: ${info.connected ? 'Yes' : 'No'}`);
                log(`   Name: ${info.name || 'Unknown'}`);
                
                showStatus(
                    `🎉 Found StayFu Extension!<br>
                     <strong>ID:</strong> ${info.extensionId}<br>
                     <strong>Status:</strong> ${info.connected ? 'Connected' : 'Not Connected'}<br>
                     <strong>Version:</strong> ${info.version || 'Unknown'}`,
                    'success'
                );
            }
        });

        async function testExtensionDetection() {
            log('🔍 Requesting extension to announce itself...');
            showStatus('🔍 Looking for StayFu extension...', 'info');
            
            // Reset detected ID
            detectedExtensionId = null;
            
            // Request extension to announce itself
            window.postMessage({
                type: 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT',
                timestamp: Date.now()
            }, '*');
            
            // Wait a bit to see if we get a response
            setTimeout(() => {
                if (!detectedExtensionId) {
                    log('❌ No extension announcement received');
                    showStatus('❌ StayFu extension not found. Make sure it\'s installed and enabled.', 'error');
                }
            }, 3000);
        }

        async function testExtensionSearch() {
            if (!detectedExtensionId) {
                showStatus('⚠️ Please detect the extension first!', 'warning');
                return;
            }

            log(`🔍 Testing search with detected extension ID: ${detectedExtensionId}`);
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome runtime not available');
                }

                chrome.runtime.sendMessage(detectedExtensionId, {
                    action: 'startSearch',
                    searchTerm: 'test'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ Search test failed: ${chrome.runtime.lastError.message}`);
                        showStatus('❌ Search test failed. Check console for details.', 'error');
                    } else {
                        log(`✅ Search test successful: ${JSON.stringify(response)}`);
                        showStatus('✅ Search test successful! Extension is working.', 'success');
                    }
                });
            } catch (error) {
                log(`❌ Search test error: ${error.message}`);
                showStatus('❌ Search test failed. This might not be a Chrome browser.', 'error');
            }
        }

        // Auto-test on page load
        setTimeout(() => {
            log('🚀 Auto-testing extension detection on page load...');
            testExtensionDetection();
        }, 1000);
    </script>
</body>
</html>
