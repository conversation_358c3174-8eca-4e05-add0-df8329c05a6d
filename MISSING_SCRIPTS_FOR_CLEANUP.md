# Missing Scripts for Safe Cleanup Process

The comprehensive cleanup plan references several critical scripts that don't exist yet. These MUST be created during Phase 0.

## Phase 0 Required Scripts

### 0. `scripts/audit-edge-functions.sh` (NEW - CRITICAL)
```bash
#!/bin/bash
echo "🔍 Auditing Edge Functions usage..."

AUDIT_FILE="docs/edge-functions-audit.md"
echo "# Edge Functions Audit - $(date)" > $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Critical Issue" >> $AUDIT_FILE
echo "App calls 25+ Edge Functions but only 1 exists locally!" >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Functions Called by App" >> $AUDIT_FILE
grep -r "functions\.invoke(" src --include="*.tsx" --include="*.ts" | \
grep -o "'[^']*'" | sort | uniq | while read func; do
  echo "- $func" >> $AUDIT_FILE
done
echo "" >> $AUDIT_FILE

echo "## Local Functions Available" >> $AUDIT_FILE
if [ -d "supabase/functions" ]; then
  ls supabase/functions/ | while read func; do
    echo "- ✅ $func (local)" >> $AUDIT_FILE
  done
else
  echo "- ❌ No local functions directory" >> $AUDIT_FILE
fi
echo "" >> $AUDIT_FILE

echo "## Missing Functions (Will Cause Failures)" >> $AUDIT_FILE
# Get list of called functions
CALLED_FUNCTIONS=$(grep -r "functions\.invoke(" src --include="*.tsx" --include="*.ts" | grep -o "'[^']*'" | sort | uniq | tr -d "'")

# Get list of local functions
if [ -d "supabase/functions" ]; then
  LOCAL_FUNCTIONS=$(ls supabase/functions/)
else
  LOCAL_FUNCTIONS=""
fi

for func in $CALLED_FUNCTIONS; do
  if ! echo "$LOCAL_FUNCTIONS" | grep -q "^$func$"; then
    echo "- ❌ $func (MISSING LOCALLY)" >> $AUDIT_FILE
  fi
done
echo "" >> $AUDIT_FILE

echo "## Recommended Strategy" >> $AUDIT_FILE
echo "1. **Hybrid Mode**: Use remote functions for missing ones" >> $AUDIT_FILE
echo "2. **Test thoroughly**: Verify no function call failures" >> $AUDIT_FILE
echo "3. **Monitor errors**: Watch for function-related failures" >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Files Using Functions" >> $AUDIT_FILE
grep -r "functions\.invoke(" src --include="*.tsx" --include="*.ts" | \
cut -d: -f1 | sort | uniq | while read file; do
  echo "- $file" >> $AUDIT_FILE
done

echo "✅ Edge Functions audit completed: $AUDIT_FILE"
echo "🚨 CRITICAL: Review this audit before proceeding with cleanup!"
```

### 1. `scripts/verify-no-regression.sh`
```bash
#!/bin/bash
set -e

echo "🔍 Running comprehensive regression checks..."

# 1. Build verification
echo "1. Testing build process..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed - STOPPING"
  exit 1
fi

# 2. Snapshot verification (don't update, just verify)
echo "2. Verifying UI snapshots..."
npm run test:snapshots -- --updateSnapshot=false
if [ $? -ne 0 ]; then
  echo "❌ Snapshots changed - UI regression detected"
  exit 1
fi

# 3. Critical functionality tests
echo "3. Testing critical functionality..."
npm run test:critical
if [ $? -ne 0 ]; then
  echo "❌ Critical functionality broken"
  exit 1
fi

# 4. Database connectivity
echo "4. Testing database connectivity..."
./scripts/verify-database-operations.sh
if [ $? -ne 0 ]; then
  echo "❌ Database operations broken"
  exit 1
fi

# 5. Dev server startup test
echo "5. Testing dev server startup..."
timeout 30s npm run dev &
DEV_PID=$!
sleep 10

# Test if server responds
curl -s http://localhost:5173 > /dev/null
if [ $? -ne 0 ]; then
  echo "❌ Dev server not responding"
  kill $DEV_PID 2>/dev/null
  exit 1
fi

kill $DEV_PID 2>/dev/null
echo "✅ All regression checks passed"
```

### 2. `scripts/audit-current-functionality.sh`
```bash
#!/bin/bash
echo "📋 Auditing current functionality..."

# Create functionality audit file
AUDIT_FILE="docs/current-functionality-audit.md"
echo "# Current Functionality Audit - $(date)" > $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Build Status" >> $AUDIT_FILE
npm run build >> $AUDIT_FILE 2>&1
echo "" >> $AUDIT_FILE

echo "## Available Routes" >> $AUDIT_FILE
grep -r "path.*:" src/pages/ | head -20 >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Database Tables" >> $AUDIT_FILE
echo "Total tables: 66" >> $AUDIT_FILE
echo "Auth records: 9,675+" >> $AUDIT_FILE
echo "Profile records: 18" >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Test Credentials" >> $AUDIT_FILE
echo "- Property Manager: <EMAIL> / Newsig1!!!" >> $AUDIT_FILE
echo "- Service Provider: <EMAIL> / Newsig1!!!" >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "## Critical User Workflows to Preserve" >> $AUDIT_FILE
echo "1. User login and authentication" >> $AUDIT_FILE
echo "2. Dashboard data loading" >> $AUDIT_FILE
echo "3. Property management (CRUD)" >> $AUDIT_FILE
echo "4. Inventory management" >> $AUDIT_FILE
echo "5. Maintenance task management" >> $AUDIT_FILE
echo "6. Team management and invitations" >> $AUDIT_FILE
echo "7. File uploads and image handling" >> $AUDIT_FILE
echo "" >> $AUDIT_FILE

echo "✅ Functionality audit completed: $AUDIT_FILE"
```

### 3. `scripts/verify-database-operations.sh`
```bash
#!/bin/bash
echo "🗄️ Verifying LOCAL Supabase database operations..."

# Test LOCAL Supabase connectivity
if ! supabase status | grep -q "local database.*HEALTHY"; then
  echo "❌ Local Supabase not running - start with 'supabase start'"
  exit 1
fi

echo "✅ Local Supabase services running on ports 54321-54324"

# Test basic operations on each major table using LOCAL database
TABLES=("properties" "inventory_items" "maintenance_tasks" "profiles" "teams")

for table in "${TABLES[@]}"; do
  echo "Testing LOCAL table: $table"
  
  # Test read operation on LOCAL PostgreSQL
  COUNT=$(psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT COUNT(*) FROM $table;" -t 2>/dev/null | tr -d ' ')
  if [ $? -ne 0 ] || [ -z "$COUNT" ]; then
    echo "❌ Failed to read from LOCAL $table"
    exit 1
  fi
  
  echo "  - LOCAL $table has $COUNT records"
done

# Verify we're using LOCAL Supabase (not production)
LOCAL_URL_CHECK=$(psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "SELECT 'LOCAL_DB_CONFIRMED';" -t 2>/dev/null | tr -d ' ')
if [ "$LOCAL_URL_CHECK" != "LOCAL_DB_CONFIRMED" ]; then
  echo "❌ Database connection issue"
  exit 1
fi

echo "✅ All LOCAL database operations verified"
echo "📍 Using isolated local database (127.0.0.1:54322) - safe for cleanup!"
```

### 4. `scripts/verify-critical-workflows.sh`
```bash
#!/bin/bash
echo "🔄 Verifying critical workflows with LOCAL Supabase..."

# Ensure LOCAL Supabase is running first
if ! supabase status | grep -q "local database.*HEALTHY"; then
  echo "❌ Local Supabase not running - start with 'supabase start'"
  exit 1
fi

# Start dev server with LOCAL Supabase environment
export VITE_SUPABASE_URL=http://127.0.0.1:54321
export VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

npm run dev &
DEV_PID=$!
sleep 20  # Give it time to start and connect to local Supabase

# Test login endpoint exists
echo "1. Testing login workflow (using LOCAL Supabase)..."
curl -s http://localhost:5173/login | grep -q "login"
if [ $? -ne 0 ]; then
  echo "❌ Login page not accessible"
  kill $DEV_PID
  exit 1
fi

# Test dashboard loads
echo "2. Testing dashboard access (with LOCAL data)..."
curl -s http://localhost:5173/ | grep -q "html"
if [ $? -ne 0 ]; then
  echo "❌ Dashboard not accessible"
  kill $DEV_PID
  exit 1
fi

# Test main app routes
ROUTES=("/properties" "/inventory" "/maintenance" "/teams")
for route in "${ROUTES[@]}"; do
  echo "3. Testing route: $route (LOCAL Supabase data)"
  curl -s "http://localhost:5173$route" | grep -q "html"
  if [ $? -ne 0 ]; then
    echo "❌ Route $route not accessible"
    kill $DEV_PID
    exit 1
  fi
done

kill $DEV_PID
echo "✅ Critical workflows verified with LOCAL Supabase"
echo "📍 App successfully connecting to local database (127.0.0.1:54321)"
```

### 5. `scripts/create-migration-rollback.sh`
```bash
#!/bin/bash
MIGRATION_NAME=$1

if [ -z "$MIGRATION_NAME" ]; then
  echo "Usage: $0 <migration-name>"
  exit 1
fi

ROLLBACK_DIR="rollbacks"
mkdir -p $ROLLBACK_DIR

ROLLBACK_FILE="$ROLLBACK_DIR/rollback_${MIGRATION_NAME}_$(date +%Y%m%d_%H%M%S).sql"

echo "-- Rollback script for: $MIGRATION_NAME" > $ROLLBACK_FILE
echo "-- Created: $(date)" >> $ROLLBACK_FILE
echo "-- IMPORTANT: Test this rollback on a copy of your data first!" >> $ROLLBACK_FILE
echo "" >> $ROLLBACK_FILE
echo "BEGIN;" >> $ROLLBACK_FILE
echo "" >> $ROLLBACK_FILE
echo "-- Add your rollback SQL commands here" >> $ROLLBACK_FILE
echo "-- Example:" >> $ROLLBACK_FILE
echo "-- DROP TABLE IF EXISTS new_table;" >> $ROLLBACK_FILE
echo "-- ALTER TABLE old_table RENAME TO original_name;" >> $ROLLBACK_FILE
echo "" >> $ROLLBACK_FILE
echo "COMMIT;" >> $ROLLBACK_FILE

echo "✅ Rollback script created: $ROLLBACK_FILE"
echo "📝 Edit this file to add your rollback commands"
```

## Environment Configuration for Local Supabase

Create `.env.local` file (for development with local Supabase):
```bash
# .env.local - Local Supabase development
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

Create `.env.test` file (for testing with local Supabase):
```bash
# .env.test - Testing with local Supabase  
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

## Package.json Script Updates Required

Add these scripts to package.json:

```json
{
  "scripts": {
    "dev:local": "VITE_SUPABASE_URL=http://127.0.0.1:54321 npm run dev",
    "test:setup": "npm install --save-dev @testing-library/react @testing-library/jest-dom vitest jsdom",
    "test:snapshots": "vitest --run --reporter=verbose tests/snapshots/",
    "test:critical": "vitest --run tests/critical/",
    "test:baseline-snapshots": "vitest --run tests/snapshots/ --reporter=verbose",
    "test:integration-with-local-supabase": "vitest --run tests/integration/",
    "test:all": "npm run test:critical && npm run test:snapshots && npm run test:integration-with-local-supabase"
  }
}
```

## Test Configuration Files Required

### `vitest.config.ts`
```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    globals: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

### `tests/setup.ts`
```typescript
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, afterAll } from 'vitest';

beforeAll(() => {
  // Setup before all tests
});

afterEach(() => {
  cleanup();
});

afterAll(() => {
  // Cleanup after all tests
});

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      single: vi.fn(),
    })),
  },
}));
```

## Critical Success Requirements

1. **ALL scripts must be executable**: `chmod +x scripts/*.sh`
2. **ALL scripts must handle errors properly**: Use `set -e` and proper error checking
3. **ALL scripts must provide clear output**: Success/failure messages with emojis
4. **ALL scripts must be tested manually** before relying on them
5. **Scripts must work with the current codebase** - no assumptions about future state

## Usage During Cleanup

During every phase of cleanup:
1. Run `./scripts/verify-no-regression.sh` after each significant change
2. If any script fails, STOP and investigate
3. Never proceed to next step if regression detected
4. Create new scripts as needed for specific safety checks

These scripts are the safety net that prevents the cleanup from breaking functionality.