import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FloatingAiAssistant from '@/components/ai/FloatingAiAssistant';
import FloatingAiToggle from '@/components/ai/FloatingAiToggle';
import { FloatingAiProvider } from '@/contexts/FloatingAiContext';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    functions: {
      invoke: vi.fn()
    }
  }
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn()
  }
}));

// Mock speech recognition
vi.mock('@/utils/speechRecognitionPolyfill', () => ({
  createSpeechRecognition: vi.fn(),
  isSpeechRecognitionSupported: vi.fn(() => false),
  getBrowserSpeechSupport: vi.fn(() => ({ message: 'Not supported' }))
}));

// Mock AI utilities
vi.mock('@/utils/enhancedAiAssistant', () => ({
  processEnhancedCommand: vi.fn(() => ({
    success: true,
    message: 'Processing command...',
    shouldProceedToBackend: true
  })),
  handleAIError: vi.fn(() => ({
    message: 'Test error',
    suggestions: ['Try again'],
    category: 'general',
    timestamp: new Date()
  })),
  generateSmartFollowUp: vi.fn((response, context, original) => `${original} - ${response}`)
}));

// Mock AI error suggestions
vi.mock('@/utils/aiErrorSuggestions', () => ({
  getCommandExamples: vi.fn(() => [
    'Add a property named Test Property',
    'Create a maintenance task',
    'Report damage'
  ])
}));

// Mock utils
vi.mock('@/lib/utils', () => ({
  cn: vi.fn((...classes) => classes.filter(Boolean).join(' ')),
  showAIConfirmationToast: vi.fn()
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const mockAuthState = {
    user: { id: 'test-user-id' },
    profile: { first_name: 'Test User' },
    isAuthenticated: true,
    isLoading: false
  };

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <div style={{ 
            // @ts-ignore - Mock auth state
            '--mock-auth-state': JSON.stringify(mockAuthState) 
          }}>
            <FloatingAiProvider>
              {children}
            </FloatingAiProvider>
          </div>
        </AuthProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

describe('FloatingAiAssistant', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should not render when not visible', () => {
    render(
      <TestWrapper>
        <FloatingAiAssistant />
      </TestWrapper>
    );

    expect(screen.queryByText('AI Assistant')).not.toBeInTheDocument();
  });

  it('should render toggle button', () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
      </TestWrapper>
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText('AI Assistant')).toBeInTheDocument();
  });

  it('should show assistant when toggle is clicked', async () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });
  });

  it('should handle command input', async () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // Find and interact with the input
    const input = screen.getByPlaceholderText(/Try:/);
    fireEvent.change(input, { target: { value: 'test command' } });

    expect(input).toHaveValue('test command');
  });

  it('should show help suggestions when help button is clicked', async () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // Click help button
    const helpButton = screen.getByText('Help');
    fireEvent.click(helpButton);

    await waitFor(() => {
      expect(screen.getByText('Command Suggestions')).toBeInTheDocument();
    });
  });

  it('should handle position changes', async () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // The position toggle should be available
    const positionButtons = screen.getAllByRole('button');
    expect(positionButtons.length).toBeGreaterThan(1);
  });

  it('should handle minimize/maximize', async () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // Should have minimize/maximize functionality
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(3); // Toggle, minimize, position, close, etc.
  });
});

describe('FloatingAiToggle', () => {
  it('should render with default props', () => {
    render(
      <TestWrapper>
        <FloatingAiToggle />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(screen.getByText('AI Assistant')).toBeInTheDocument();
  });

  it('should render without label when showLabel is false', () => {
    render(
      <TestWrapper>
        <FloatingAiToggle showLabel={false} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(screen.queryByText('AI Assistant')).not.toBeInTheDocument();
  });

  it('should apply custom className', () => {
    render(
      <TestWrapper>
        <FloatingAiToggle className="custom-class" />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });
});

describe('Enhanced AI Features', () => {
  it('should handle error states', async () => {
    const { processEnhancedCommand } = await import('@/utils/enhancedAiAssistant');
    
    // Mock an error response
    vi.mocked(processEnhancedCommand).mockReturnValue({
      success: false,
      message: 'Error occurred',
      error: {
        message: 'Test error',
        suggestions: ['Try again', 'Check input'],
        category: 'general',
        timestamp: new Date()
      },
      shouldProceedToBackend: false
    });

    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // The error handling should be integrated into the component
    expect(processEnhancedCommand).toBeDefined();
  });

  it('should handle follow-up questions', async () => {
    const { processEnhancedCommand } = await import('@/utils/enhancedAiAssistant');
    
    // Mock a follow-up question response
    vi.mocked(processEnhancedCommand).mockReturnValue({
      success: false,
      message: 'Need more information',
      followUpQuestions: [{
        id: 'test-question',
        question: 'Which property?',
        options: ['Property A', 'Property B'],
        context: 'property_selection',
        timestamp: new Date()
      }],
      shouldProceedToBackend: false
    });

    render(
      <TestWrapper>
        <FloatingAiToggle />
        <FloatingAiAssistant />
      </TestWrapper>
    );

    // Show the assistant
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });

    // The follow-up question handling should be integrated
    expect(processEnhancedCommand).toBeDefined();
  });
});
