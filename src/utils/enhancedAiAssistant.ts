// Enhanced AI Assistant with better error handling and follow-up questions
import { AIError, FollowUpQuestion } from '@/contexts/FloatingAiContext';
import { getErrorSuggestions, suggestSimilarCommands, getCommandExamples } from './aiErrorSuggestions';
import { processCommandLocally, LocalAIResponse } from './aiLocalAssistant';

export interface EnhancedAIResponse {
  success: boolean;
  message: string;
  error?: AIError;
  followUpQuestions?: FollowUpQuestion[];
  shouldProceedToBackend: boolean;
  localResponse?: LocalAIResponse;
}

export const processEnhancedCommand = (
  command: string,
  conversationHistory: string[] = []
): EnhancedAIResponse => {
  const trimmedCommand = command.trim();
  
  if (!trimmedCommand) {
    return {
      success: false,
      message: "Please enter a command",
      error: {
        message: "Empty command detected",
        suggestions: [
          "Try typing a command like 'Add a new property'",
          "Use 'help' to see available commands",
          "Ask me to create, update, or manage your properties"
        ],
        category: 'syntax',
        timestamp: new Date()
      },
      shouldProceedToBackend: false
    };
  }

  // Process with local AI first
  const localResult = processCommandLocally(trimmedCommand);
  
  if (!localResult.shouldProceed) {
    // Local AI found issues - provide enhanced feedback
    const followUpQuestions = generateFollowUpQuestions(trimmedCommand, localResult);
    
    return {
      success: false,
      message: localResult.response.message,
      error: {
        message: localResult.response.message,
        suggestions: localResult.response.suggestions || [],
        category: 'context',
        timestamp: new Date()
      },
      followUpQuestions,
      shouldProceedToBackend: false,
      localResponse: localResult
    };
  }

  // Check for ambiguous commands that need clarification
  const ambiguityCheck = checkForAmbiguity(trimmedCommand, conversationHistory);
  if (ambiguityCheck.needsClarification) {
    return {
      success: false,
      message: ambiguityCheck.message,
      followUpQuestions: ambiguityCheck.followUpQuestions,
      shouldProceedToBackend: false
    };
  }

  // Command looks good - proceed to backend
  return {
    success: true,
    message: "Processing your command...",
    shouldProceedToBackend: true,
    localResponse: localResult
  };
};

export const handleAIError = (
  error: Error,
  command: string,
  conversationHistory: string[] = []
): AIError => {
  const errorSuggestion = getErrorSuggestions(error.message, command);
  
  // Enhance suggestions based on conversation history
  const enhancedSuggestions = enhanceSuggestionsWithHistory(
    errorSuggestion.suggestions,
    command,
    conversationHistory
  );

  return {
    message: errorSuggestion.message,
    suggestions: enhancedSuggestions,
    category: errorSuggestion.category,
    timestamp: new Date()
  };
};

const generateFollowUpQuestions = (
  command: string,
  localResult: LocalAIResponse
): FollowUpQuestion[] => {
  const questions: FollowUpQuestion[] = [];
  const lowerCommand = command.toLowerCase();

  // Property-related follow-ups
  if (lowerCommand.includes('property') || lowerCommand.includes('add')) {
    if (localResult.response.missingInfo?.includes('address')) {
      questions.push({
        id: `followup_${Date.now()}_address`,
        question: "What's the address of the property?",
        context: "property_address",
        timestamp: new Date()
      });
    }
    
    if (localResult.response.missingInfo?.includes('bedrooms')) {
      questions.push({
        id: `followup_${Date.now()}_bedrooms`,
        question: "How many bedrooms does it have?",
        options: ["1", "2", "3", "4", "5+"],
        context: "property_bedrooms",
        timestamp: new Date()
      });
    }
  }

  // Maintenance-related follow-ups
  if (lowerCommand.includes('maintenance') || lowerCommand.includes('fix') || lowerCommand.includes('repair')) {
    if (!lowerCommand.includes('property') && !lowerCommand.includes('at')) {
      questions.push({
        id: `followup_${Date.now()}_property`,
        question: "Which property needs maintenance?",
        context: "maintenance_property",
        timestamp: new Date()
      });
    }
    
    if (!lowerCommand.includes('urgent') && !lowerCommand.includes('critical') && !lowerCommand.includes('low')) {
      questions.push({
        id: `followup_${Date.now()}_priority`,
        question: "What's the priority level?",
        options: ["Low", "Medium", "High", "Critical"],
        context: "maintenance_priority",
        timestamp: new Date()
      });
    }
  }

  // Damage report follow-ups
  if (lowerCommand.includes('damage')) {
    if (!lowerCommand.includes('property') && !lowerCommand.includes('at')) {
      questions.push({
        id: `followup_${Date.now()}_damage_property`,
        question: "Which property has the damage?",
        context: "damage_property",
        timestamp: new Date()
      });
    }
    
    if (!lowerCommand.includes('room') && !lowerCommand.includes('kitchen') && !lowerCommand.includes('bathroom')) {
      questions.push({
        id: `followup_${Date.now()}_damage_location`,
        question: "Where in the property is the damage?",
        options: ["Living Room", "Kitchen", "Bathroom", "Bedroom", "Other"],
        context: "damage_location",
        timestamp: new Date()
      });
    }
  }

  // Inventory follow-ups
  if (lowerCommand.includes('inventory') || lowerCommand.includes('stock')) {
    if (!lowerCommand.includes('property') && !lowerCommand.includes('at')) {
      questions.push({
        id: `followup_${Date.now()}_inventory_property`,
        question: "Which property's inventory?",
        context: "inventory_property",
        timestamp: new Date()
      });
    }
  }

  return questions;
};

const checkForAmbiguity = (
  command: string,
  conversationHistory: string[]
): { needsClarification: boolean; message?: string; followUpQuestions?: FollowUpQuestion[] } => {
  const lowerCommand = command.toLowerCase();
  
  // Check for vague commands
  const vaguePhrases = ['fix it', 'update that', 'change this', 'the property', 'that one'];
  const hasVaguePhrase = vaguePhrases.some(phrase => lowerCommand.includes(phrase));
  
  if (hasVaguePhrase && conversationHistory.length === 0) {
    return {
      needsClarification: true,
      message: "I need more specific information to help you.",
      followUpQuestions: [{
        id: `clarification_${Date.now()}`,
        question: "Could you be more specific about what you'd like to do?",
        options: [
          "Add a new property",
          "Create a maintenance task", 
          "Report damage",
          "Manage inventory",
          "Create a purchase order"
        ],
        context: "command_clarification",
        timestamp: new Date()
      }]
    };
  }

  // Check for commands that reference previous context without enough history
  const contextualPhrases = ['also', 'too', 'as well', 'same property', 'that property'];
  const needsContext = contextualPhrases.some(phrase => lowerCommand.includes(phrase));
  
  if (needsContext && conversationHistory.length < 2) {
    return {
      needsClarification: true,
      message: "I don't have enough context from our conversation to understand what you're referring to.",
      followUpQuestions: [{
        id: `context_${Date.now()}`,
        question: "Could you provide more details about what you're referring to?",
        context: "missing_context",
        timestamp: new Date()
      }]
    };
  }

  return { needsClarification: false };
};

const enhanceSuggestionsWithHistory = (
  baseSuggestions: string[],
  command: string,
  conversationHistory: string[]
): string[] => {
  const enhanced = [...baseSuggestions];
  
  // Add context-aware suggestions based on recent conversation
  if (conversationHistory.length > 0) {
    const recentCommands = conversationHistory.slice(-3);
    const hasPropertyMention = recentCommands.some(cmd => 
      cmd.toLowerCase().includes('property') || cmd.toLowerCase().includes('at ')
    );
    
    if (hasPropertyMention && !command.toLowerCase().includes('property')) {
      enhanced.unshift("Try referencing the property mentioned in our previous conversation");
    }
  }
  
  // Add command-specific contextual suggestions
  const lowerCommand = command.toLowerCase();
  if (lowerCommand.includes('error') || lowerCommand.includes('failed')) {
    enhanced.push("Check if you have the necessary permissions for this action");
    enhanced.push("Verify that all required information is provided");
  }
  
  return enhanced;
};

export const generateSmartFollowUp = (
  userResponse: string,
  questionContext: string,
  originalCommand: string
): string => {
  // Combine the user's response with the original command context
  const contextMap: Record<string, string> = {
    'property_address': `${originalCommand} at ${userResponse}`,
    'property_bedrooms': `${originalCommand} with ${userResponse} bedrooms`,
    'maintenance_property': `${originalCommand} at ${userResponse}`,
    'maintenance_priority': `${originalCommand} with ${userResponse.toLowerCase()} priority`,
    'damage_property': `${originalCommand} at ${userResponse}`,
    'damage_location': `${originalCommand} in the ${userResponse.toLowerCase()}`,
    'inventory_property': `${originalCommand} at ${userResponse}`,
    'command_clarification': userResponse,
    'missing_context': `${originalCommand} - ${userResponse}`
  };
  
  return contextMap[questionContext] || `${originalCommand} - ${userResponse}`;
};

export const shouldShowFollowUpQuestions = (
  command: string,
  conversationHistory: string[]
): boolean => {
  // Don't show follow-ups for simple or complete commands
  const simpleCommands = ['help', 'clear', 'history'];
  if (simpleCommands.includes(command.toLowerCase().trim())) {
    return false;
  }
  
  // Don't show follow-ups if the command seems complete
  const hasSpecificDetails = command.length > 20 && 
    (command.includes(' at ') || command.includes(' for ') || command.includes(' with '));
  
  return !hasSpecificDetails;
};
