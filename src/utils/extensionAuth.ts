/**
 * Utility functions for automatically managing extension authentication
 */

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Automatically generates and provides an API token to the extension
 */
export const autoConfigureExtension = async (): Promise<boolean> => {
  try {
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      toast.error('You must be logged in to configure the extension');
      return false;
    }

    // Generate a new API token using the Supabase function
    const { data: tokenData, error: tokenError } = await supabase.functions.invoke('generate-extension-token');
    
    if (tokenError || !tokenData?.token) {
      console.error('Error generating token:', tokenError);
      toast.error('Failed to generate API token');
      return false;
    }

    // Send configuration to extension
    const extensionConfigured = await configureExtensionWithToken(tokenData.token);
    
    if (extensionConfigured) {
      toast.success('Extension configured automatically! You can now search Amazon products.');
      return true;
    } else {
      // Fallback: copy token to clipboard for manual setup
      await navigator.clipboard.writeText(tokenData.token);
      toast.info('API token copied to clipboard. Please paste it in the extension settings.');
      return false;
    }
  } catch (error) {
    console.error('Error in autoConfigureExtension:', error);
    toast.error('Failed to auto-configure extension');
    return false;
  }
};

/**
 * Sends configuration directly to the extension
 */
const configureExtensionWithToken = async (token: string): Promise<boolean> => {
  try {
    // Try to communicate with extension
    const currentUrl = window.location.origin;
    
    // Check if Chrome extension API is available
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      console.log('Chrome extension API not available');
      return false;
    }

    // Try to find StayFu extension
    const response = await new Promise<any>((resolve) => {
      // Listen for extension announcement
      const handleExtensionAnnouncement = (event: MessageEvent) => {
        if (event.data.type === 'STAYFU_EXTENSION_ANNOUNCEMENT' && event.data.extensionId) {
          window.removeEventListener('message', handleExtensionAnnouncement);
          clearTimeout(timeoutId);
          resolve({ extensionId: event.data.extensionId });
        }
      };

      window.addEventListener('message', handleExtensionAnnouncement);

      // Request extension to announce itself
      window.postMessage({
        type: 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT',
        timestamp: Date.now()
      }, '*');

      // Timeout after 3 seconds
      const timeoutId = setTimeout(() => {
        window.removeEventListener('message', handleExtensionAnnouncement);
        resolve(null);
      }, 3000);
    });

    if (!response?.extensionId) {
      console.log('Extension not found or not responding');
      return false;
    }

    // Configure the extension
    const configureResult = await new Promise<boolean>((resolve) => {
      try {
        chrome.runtime.sendMessage(response.extensionId, {
          action: 'autoConfigureExtension',
          url: currentUrl,
          token: token
        }, (configResponse) => {
          if (chrome.runtime.lastError) {
            console.error('Error configuring extension:', chrome.runtime.lastError);
            resolve(false);
            return;
          }
          resolve(configResponse?.success === true);
        });
      } catch (error) {
        console.error('Error sending message to extension:', error);
        resolve(false);
      }
    });

    return configureResult;
  } catch (error) {
    console.error('Error in configureExtensionWithToken:', error);
    return false;
  }
};

/**
 * Checks if extension is installed and properly configured
 */
export const checkExtensionStatus = async (): Promise<{
  installed: boolean;
  configured: boolean;
  extensionId?: string;
}> => {
  try {
    // Try to detect extension
    const response = await new Promise<any>((resolve) => {
      const handleExtensionAnnouncement = (event: MessageEvent) => {
        if (event.data.type === 'STAYFU_EXTENSION_ANNOUNCEMENT' && event.data.extensionId) {
          window.removeEventListener('message', handleExtensionAnnouncement);
          clearTimeout(timeoutId);
          resolve({
            extensionId: event.data.extensionId,
            connected: event.data.connected
          });
        }
      };

      window.addEventListener('message', handleExtensionAnnouncement);

      window.postMessage({
        type: 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT',
        timestamp: Date.now()
      }, '*');

      const timeoutId = setTimeout(() => {
        window.removeEventListener('message', handleExtensionAnnouncement);
        resolve(null);
      }, 3000);
    });

    if (!response) {
      return { installed: false, configured: false };
    }

    return {
      installed: true,
      configured: response.connected === true,
      extensionId: response.extensionId
    };
  } catch (error) {
    console.error('Error checking extension status:', error);
    return { installed: false, configured: false };
  }
};
