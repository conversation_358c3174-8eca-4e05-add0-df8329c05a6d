// AI Error Suggestions and Enhanced Error Messaging

export interface ErrorSuggestion {
  message: string;
  suggestions: string[];
  category: 'syntax' | 'context' | 'permission' | 'data' | 'network' | 'general';
}

export const getErrorSuggestions = (error: string, command: string): ErrorSuggestion => {
  const lowerError = error.toLowerCase();
  const lowerCommand = command.toLowerCase();

  // Network/API related errors
  if (lowerError.includes('network') || lowerError.includes('fetch') || lowerError.includes('connection')) {
    return {
      message: "Network connection issue detected",
      suggestions: [
        "Check your internet connection",
        "Try the command again in a few moments",
        "Ensure you're logged in properly"
      ],
      category: 'network'
    };
  }

  // Authentication/Permission errors
  if (lowerError.includes('auth') || lowerError.includes('permission') || lowerError.includes('unauthorized')) {
    return {
      message: "Authentication or permission issue",
      suggestions: [
        "Make sure you're logged in",
        "Check if you have permission for this action",
        "Try refreshing the page and logging in again"
      ],
      category: 'permission'
    };
  }

  // Property-related command errors
  if (lowerCommand.includes('property') || lowerCommand.includes('add') && lowerCommand.includes('bedroom')) {
    return {
      message: "Property command needs more information",
      suggestions: [
        `Try: "Add a property named [Name] at [Address] with [X] bedrooms"`,
        `Example: "Add a property named Ocean View at 123 Beach Road with 3 bedrooms"`,
        "Make sure to include property name, address, and bedroom count"
      ],
      category: 'syntax'
    };
  }

  // Maintenance-related command errors
  if (lowerCommand.includes('maintenance') || lowerCommand.includes('fix') || lowerCommand.includes('repair')) {
    return {
      message: "Maintenance command needs clarification",
      suggestions: [
        `Try: "Create maintenance task to [action] at [property name]"`,
        `Example: "Create a maintenance task to fix the broken sink at Mountain Cabin"`,
        "Include what needs to be done and which property"
      ],
      category: 'syntax'
    };
  }

  // Inventory-related command errors
  if (lowerCommand.includes('inventory') || lowerCommand.includes('stock') || lowerCommand.includes('towel') || lowerCommand.includes('minimum')) {
    return {
      message: "Inventory command format issue",
      suggestions: [
        `Try: "We're down to [current amount] [item], we need a minimum of [target]"`,
        `Example: "We're down to only 2 bath towels, we need a minimum of 12"`,
        "Specify current quantity, item name, and target minimum"
      ],
      category: 'syntax'
    };
  }

  // Purchase order related errors
  if (lowerCommand.includes('purchase') || lowerCommand.includes('order') || lowerCommand.includes('buy')) {
    return {
      message: "Purchase order command needs details",
      suggestions: [
        `Try: "Create a purchase order for [items]"`,
        `Example: "Create a purchase order for all low stock items"`,
        "Or: \"Order more cleaning supplies\""
      ],
      category: 'syntax'
    };
  }

  // Collection related errors
  if (lowerCommand.includes('collection') || lowerCommand.includes('budget')) {
    return {
      message: "Collection command format issue",
      suggestions: [
        `Try: "Create a new [type] collection with a budget of $[amount]"`,
        `Example: "Create a new kitchen collection with a budget of $500"`,
        "Specify collection type and budget amount"
      ],
      category: 'syntax'
    };
  }

  // Data validation errors
  if (lowerError.includes('invalid') || lowerError.includes('format') || lowerError.includes('parse')) {
    return {
      message: "Command format or data issue",
      suggestions: [
        "Check your command syntax and try again",
        "Make sure all required information is included",
        "Use natural language like you're talking to a person"
      ],
      category: 'data'
    };
  }

  // Context-related errors (when AI can't understand)
  if (lowerError.includes('understand') || lowerError.includes('process') || lowerError.includes('unclear')) {
    return {
      message: "AI couldn't understand your request",
      suggestions: [
        "Try being more specific about what you want to do",
        "Include details like property names, quantities, or descriptions",
        "Use commands like: 'Add', 'Create', 'Update', or 'Fix'"
      ],
      category: 'context'
    };
  }

  // General fallback suggestions
  return {
    message: "Something went wrong processing your command",
    suggestions: [
      "Try rephrasing your request more clearly",
      "Make sure you include all necessary details",
      "Check the conversation history for context",
      "Contact support if the issue persists"
    ],
    category: 'general'
  };
};

export const getCommandExamples = (currentPath?: string): string[] => {
  // Context-aware examples based on current page
  if (currentPath?.includes('/properties')) {
    return [
      "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
      "Create a damage report at Beach House for a wine stain on the living room carpet",
      "Update the Beach House property to have 4 bedrooms instead of 3",
      "Add a new property named Sunset Villa with 2 bedrooms"
    ];
  } else if (currentPath?.includes('/maintenance')) {
    return [
      "Create a maintenance task to fix the broken sink at Beach House",
      "Assign the HVAC repair task to John Smith",
      "Mark the plumbing task as completed at Ocean View",
      "Create a high priority maintenance task for the broken AC at Beach House"
    ];
  } else if (currentPath?.includes('/inventory')) {
    return [
      "We're down to only 2 bath towels, we need a minimum of 12",
      "Add 5 more wine glasses to inventory",
      "Create a purchase order for all low stock items",
      "Add 10 towels to the Beach House bathroom collection"
    ];
  } else if (currentPath?.includes('/damages')) {
    return [
      "Create a damage report at Beach House for a wine stain on the living room carpet",
      "Update the carpet damage status to resolved",
      "Generate an invoice for the Beach House carpet repair",
      "Create a damage report for broken window at Ocean View"
    ];
  } else if (currentPath?.includes('/purchase-orders')) {
    return [
      "Create a purchase order for all low stock items",
      "Add cleaning supplies to the pending purchase order",
      "Mark the towel order as received",
      "Order more kitchen supplies for Beach House"
    ];
  }

  // Default examples for dashboard and other pages
  return [
    "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
    "Create a maintenance task to fix the broken sink at Beach House",
    "Create a damage report at Beach House for a wine stain on the living room carpet",
    "We're down to only 2 bath towels, we need a minimum of 12",
    "Create a purchase order for all low stock items",
    "Create a new kitchen collection with a budget of $500",
    "Add 5 more wine glasses to inventory",
    "Order cleaning supplies for the downtown apartment"
  ];
};

export const suggestSimilarCommands = (command: string): string[] => {
  const lowerCommand = command.toLowerCase();
  const suggestions: string[] = [];

  // If user mentions add/create
  if (lowerCommand.includes('add') || lowerCommand.includes('create')) {
    if (lowerCommand.includes('property')) {
      suggestions.push("Add a property named [Name] at [Address] with [X] bedrooms");
    }
    if (lowerCommand.includes('damage') || lowerCommand.includes('damage report')) {
      suggestions.push("Create a damage report at [property] for [damage description] on/in [location]");
    }
    if (lowerCommand.includes('maintenance') || lowerCommand.includes('task')) {
      suggestions.push("Create a maintenance task to [action] at [property]");
    }
    if (lowerCommand.includes('collection')) {
      suggestions.push("Create a new [type] collection with a budget of $[amount]");
    }
    if (lowerCommand.includes('order') || lowerCommand.includes('purchase')) {
      suggestions.push("Create a purchase order for [items or 'all low stock items']");
    }
  }

  // If user mentions inventory/stock
  if (lowerCommand.includes('inventory') || lowerCommand.includes('stock') || lowerCommand.includes('need')) {
    suggestions.push("We're down to [current] [item], we need a minimum of [target]");
    suggestions.push("Add [quantity] more [item] to inventory");
  }

  // If user mentions fix/repair/maintenance
  if (lowerCommand.includes('fix') || lowerCommand.includes('repair') || lowerCommand.includes('broken')) {
    suggestions.push("Create a maintenance task to fix the [issue] at [property]");
    suggestions.push("Schedule maintenance for the [system/item] at [property]");
  }

  // Return first 3 suggestions or default examples
  return suggestions.slice(0, 3).length > 0 ? suggestions.slice(0, 3) : getCommandExamples().slice(0, 3);
};