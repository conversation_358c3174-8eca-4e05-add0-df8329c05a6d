/**
 * Utility functions for detecting and communicating with the StayFu Chrome extension
 * using dynamic extension announcement instead of hardcoded IDs
 */

export interface ExtensionInfo {
  id: string;
  isInstalled: boolean;
  isConnected: boolean;
  version?: string;
  name?: string;
}

/**
 * Listens for the StayFu extension to announce itself
 * This is much more reliable than trying to guess extension IDs
 */
export const detectStayFuExtension = (): Promise<ExtensionInfo | null> => {
  return new Promise((resolve) => {
    console.log('[ExtensionDetection] Requesting StayFu extension to announce itself...');

    // Set up a listener for extension announcements
    const handleExtensionAnnouncement = (event: MessageEvent) => {
      if (event.data.type === 'STAYFU_EXTENSION_ANNOUNCEMENT' && event.data.extensionId) {
        console.log('[ExtensionDetection] Received extension announcement:', event.data);
        
        const extensionInfo: ExtensionInfo = {
          id: event.data.extensionId,
          isInstalled: true,
          isConnected: event.data.connected === true,
          version: event.data.version,
          name: event.data.name || 'StayFu Extension'
        };

        // Clean up listener
        window.removeEventListener('message', handleExtensionAnnouncement);
        clearTimeout(timeoutId);
        
        resolve(extensionInfo);
      }
    };

    // Listen for announcements
    window.addEventListener('message', handleExtensionAnnouncement);

    // Request all StayFu extensions to announce themselves
    window.postMessage({
      type: 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT',
      timestamp: Date.now()
    }, '*');

    // Timeout after 3 seconds if no announcement received
    const timeoutId = setTimeout(() => {
      console.log('[ExtensionDetection] No extension announcement received within timeout');
      window.removeEventListener('message', handleExtensionAnnouncement);
      resolve(null);
    }, 3000);
  });
};

/**
 * Pings a specific extension ID to check if it's installed and connected
 * (Kept for backward compatibility)
 */
export const pingExtension = (extensionId: string): Promise<ExtensionInfo> => {
  return new Promise((resolve) => {
    if (!extensionId || typeof extensionId !== 'string') {
      resolve({ id: extensionId, isInstalled: false, isConnected: false });
      return;
    }

    // Check if Chrome runtime is available
    if (typeof chrome === 'undefined' || !chrome.runtime?.sendMessage) {
      resolve({ id: extensionId, isInstalled: false, isConnected: false });
      return;
    }

    try {
      chrome.runtime.sendMessage(extensionId, { action: 'ping' }, (pingResponse) => {
        if (chrome.runtime.lastError) {
          // Extension not found or not responding
          resolve({ id: extensionId, isInstalled: false, isConnected: false });
          return;
        }

        if (!pingResponse?.success) {
          // Extension responded but not successfully
          resolve({ id: extensionId, isInstalled: false, isConnected: false });
          return;
        }

        // Extension is installed, now check connection
        chrome.runtime.sendMessage(extensionId, { action: 'checkStayfuConnection' }, (connectionResponse) => {
          if (chrome.runtime.lastError) {
            // Extension installed but connection check failed
            resolve({ id: extensionId, isInstalled: true, isConnected: false });
            return;
          }

          const isConnected = connectionResponse?.connected === true;
          resolve({ 
            id: extensionId, 
            isInstalled: true, 
            isConnected,
            version: connectionResponse?.version,
            name: connectionResponse?.name
          });
        });
      });
    } catch (error) {
      resolve({ id: extensionId, isInstalled: false, isConnected: false });
    }
  });
};

/**
 * Sends a message to the detected extension using its announced ID
 */
export const sendMessageToExtension = async (message: any): Promise<any> => {
  const extensionInfo = await detectStayFuExtension();
  
  if (!extensionInfo?.isInstalled) {
    throw new Error('StayFu extension not found or not installed');
  }

  // Check if Chrome runtime is available
  if (typeof chrome === 'undefined' || !chrome.runtime?.sendMessage) {
    throw new Error('Chrome runtime not available');
  }

  return new Promise((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(extensionInfo.id, message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(response);
      });
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Stores the detected extension info for future use
 */
let cachedExtensionInfo: ExtensionInfo | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 30000; // 30 seconds

export const getCachedExtensionInfo = (): ExtensionInfo | null => {
  // Check if cache is still valid
  if (cachedExtensionInfo && Date.now() - cacheTimestamp < CACHE_DURATION) {
    return cachedExtensionInfo;
  }
  
  // Cache expired or doesn't exist
  return null;
};

export const setCachedExtensionInfo = (info: ExtensionInfo | null): void => {
  cachedExtensionInfo = info;
  cacheTimestamp = Date.now();
};

export const clearCachedExtensionInfo = (): void => {
  cachedExtensionInfo = null;
  cacheTimestamp = 0;
};
