import React, { createContext, useContext, useReducer, ReactNode, useCallback } from 'react';
import { ConversationMessage } from './AiConversationContext';

export interface AIError {
  message: string;
  suggestions: string[];
  category: 'syntax' | 'context' | 'permission' | 'data' | 'network' | 'general';
  timestamp: Date;
}

export interface FollowUpQuestion {
  id: string;
  question: string;
  options?: string[];
  context: string;
  timestamp: Date;
}

export interface FloatingAiState {
  isMinimized: boolean;
  isFloating: boolean; // Whether it's floating due to scroll
  messages: ConversationMessage[];
  sessionId: string;
  currentError: AIError | null;
  followUpQuestions: FollowUpQuestion[];
  isLoading: boolean;
  lastCommand: string;
}

type FloatingAiAction =
  | { type: 'TOGGLE_MINIMIZED' }
  | { type: 'SET_MINIMIZED'; payload: boolean }
  | { type: 'SET_FLOATING'; payload: boolean }
  | { type: 'ADD_MESSAGE'; payload: ConversationMessage }
  | { type: 'CLEAR_CONVERSATION' }
  | { type: 'SET_SESSION_ID'; payload: string }
  | { type: 'SET_ERROR'; payload: AIError | null }
  | { type: 'ADD_FOLLOW_UP_QUESTION'; payload: FollowUpQuestion }
  | { type: 'CLEAR_FOLLOW_UP_QUESTIONS' }
  | { type: 'REMOVE_FOLLOW_UP_QUESTION'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_LAST_COMMAND'; payload: string };

const floatingAiReducer = (state: FloatingAiState, action: FloatingAiAction): FloatingAiState => {
  switch (action.type) {
    case 'TOGGLE_MINIMIZED':
      return { ...state, isMinimized: !state.isMinimized };
    case 'SET_MINIMIZED':
      return { ...state, isMinimized: action.payload };
    case 'SET_FLOATING':
      return { ...state, isFloating: action.payload };
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
        currentError: null // Clear error when new message is added
      };
    case 'CLEAR_CONVERSATION':
      return {
        ...state,
        messages: [],
        sessionId: generateSessionId(),
        currentError: null,
        followUpQuestions: []
      };
    case 'SET_SESSION_ID':
      return { ...state, sessionId: action.payload };
    case 'SET_ERROR':
      return { ...state, currentError: action.payload };
    case 'ADD_FOLLOW_UP_QUESTION':
      return {
        ...state,
        followUpQuestions: [...state.followUpQuestions, action.payload]
      };
    case 'CLEAR_FOLLOW_UP_QUESTIONS':
      return { ...state, followUpQuestions: [] };
    case 'REMOVE_FOLLOW_UP_QUESTION':
      return {
        ...state,
        followUpQuestions: state.followUpQuestions.filter(q => q.id !== action.payload)
      };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_LAST_COMMAND':
      return { ...state, lastCommand: action.payload };
    default:
      return state;
  }
};

const generateSessionId = (): string => {
  return `floating_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Load preferences from localStorage
const loadPreferences = () => {
  try {
    const saved = localStorage.getItem('ai-assistant-preferences');
    if (saved) {
      const prefs = JSON.parse(saved);
      return {
        isMinimized: prefs.isMinimized ?? false
      };
    }
  } catch (error) {
    console.warn('Failed to load AI assistant preferences:', error);
  }
  return {
    isMinimized: false
  };
};

// Save preferences to localStorage
const savePreferences = (state: FloatingAiState) => {
  try {
    const prefs = {
      isMinimized: state.isMinimized
    };
    localStorage.setItem('ai-assistant-preferences', JSON.stringify(prefs));
  } catch (error) {
    console.warn('Failed to save AI assistant preferences:', error);
  }
};

const initialPrefs = loadPreferences();

const initialState: FloatingAiState = {
  isMinimized: initialPrefs.isMinimized,
  isFloating: false,
  messages: [],
  sessionId: generateSessionId(),
  currentError: null,
  followUpQuestions: [],
  isLoading: false,
  lastCommand: ''
};

interface FloatingAiContextType {
  state: FloatingAiState;
  toggleMinimized: () => void;
  setMinimized: (minimized: boolean) => void;
  setFloating: (floating: boolean) => void;
  addMessage: (message: Omit<ConversationMessage, 'id'>) => void;
  clearConversation: () => void;
  getConversationContext: () => string;
  setError: (error: AIError | null) => void;
  addFollowUpQuestion: (question: FollowUpQuestion) => void;
  clearFollowUpQuestions: () => void;
  removeFollowUpQuestion: (questionId: string) => void;
  setLoading: (loading: boolean) => void;
  setLastCommand: (command: string) => void;
  processCommand: (command: string) => Promise<void>;
}

const FloatingAiContext = createContext<FloatingAiContextType | undefined>(undefined);

export const FloatingAiProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(floatingAiReducer, initialState);

  // Save preferences to localStorage whenever they change
  React.useEffect(() => {
    savePreferences(state);
  }, [state.isMinimized]);

  const toggleMinimized = useCallback(() => {
    dispatch({ type: 'TOGGLE_MINIMIZED' });
  }, []);

  const setMinimized = useCallback((minimized: boolean) => {
    dispatch({ type: 'SET_MINIMIZED', payload: minimized });
  }, []);

  const setFloating = useCallback((floating: boolean) => {
    dispatch({ type: 'SET_FLOATING', payload: floating });
  }, []);

  const addMessage = useCallback((message: Omit<ConversationMessage, 'id'>) => {
    const messageWithId: ConversationMessage = {
      ...message,
      id: `floating_msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    dispatch({ type: 'ADD_MESSAGE', payload: messageWithId });
  }, []);

  const clearConversation = useCallback(() => {
    dispatch({ type: 'CLEAR_CONVERSATION' });
  }, []);

  const getConversationContext = useCallback((): string => {
    // Get last 5 messages for context, excluding system messages
    const recentMessages = state.messages.slice(-5);
    
    if (recentMessages.length === 0) return '';
    
    const contextMessages = recentMessages.map(msg => 
      `${msg.type === 'user' ? 'User' : 'AI'}: ${msg.content}`
    ).join('\n');
    
    return `Previous conversation context:\n${contextMessages}\n\nCurrent request:`;
  }, [state.messages]);

  const setError = useCallback((error: AIError | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const addFollowUpQuestion = useCallback((question: FollowUpQuestion) => {
    dispatch({ type: 'ADD_FOLLOW_UP_QUESTION', payload: question });
  }, []);

  const clearFollowUpQuestions = useCallback(() => {
    dispatch({ type: 'CLEAR_FOLLOW_UP_QUESTIONS' });
  }, []);

  const removeFollowUpQuestion = useCallback((questionId: string) => {
    dispatch({ type: 'REMOVE_FOLLOW_UP_QUESTION', payload: questionId });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  const setLastCommand = useCallback((command: string) => {
    dispatch({ type: 'SET_LAST_COMMAND', payload: command });
  }, []);

  const processCommand = useCallback(async (command: string) => {
    // This will be implemented in the component that uses this context
    // For now, it's a placeholder to maintain the interface
    console.log('Processing command:', command);
  }, []);

  return (
    <FloatingAiContext.Provider value={{
      state,
      toggleMinimized,
      setMinimized,
      setFloating,
      addMessage,
      clearConversation,
      getConversationContext,
      setError,
      addFollowUpQuestion,
      clearFollowUpQuestions,
      removeFollowUpQuestion,
      setLoading,
      setLastCommand,
      processCommand
    }}>
      {children}
    </FloatingAiContext.Provider>
  );
};

export const useFloatingAi = (): FloatingAiContextType => {
  const context = useContext(FloatingAiContext);
  if (context === undefined) {
    throw new Error('useFloatingAi must be used within a FloatingAiProvider');
  }
  return context;
};
