
import { useState, useCallback, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { FormattedInventoryItem } from '@/types/inventory';

export interface FilterOptions {
  property: string;
  collection: string;
  stockStatus: string;
}

export const useInventoryFilters = (items: FormattedInventoryItem[]) => {
  const [filters, setFilters] = useState<FilterOptions>({
    property: '',
    collection: '',
    stockStatus: 'all'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [propertyCollections, setPropertyCollections] = useState<string[]>([]);

  // Fetch collections from property definitions
  useEffect(() => {
    const fetchPropertyCollections = async () => {
      try {
        const { data: propertiesData, error } = await supabase
          .from('properties')
          .select('collections')
          .not('collections', 'is', null);

        if (error) {
          console.error('Error fetching property collections:', error);
          return;
        }

        // Extract unique collections from all properties
        const uniqueCollections = new Set<string>();
        
        propertiesData?.forEach((property: { collections: any[] }) => {
          if (property.collections && Array.isArray(property.collections)) {
            property.collections.forEach((col: any) => {
              const collectionName = typeof col === 'object' ? col.name : col;
              if (collectionName && typeof collectionName === 'string') {
                uniqueCollections.add(collectionName);
              }
            });
          }
        });

        setPropertyCollections(Array.from(uniqueCollections).sort());
      } catch (error) {
        console.error('Error in fetchPropertyCollections:', error);
      }
    };

    fetchPropertyCollections();
  }, []);

  // Get unique property names and collections for filter dropdowns
  const properties = [...new Set(items.map(item => item.propertyName).filter(Boolean))].sort();
  
  // Combine collections from both property definitions and existing inventory items
  const collections = useMemo(() => {
    const inventoryCollections = [...new Set(items.map(item => item.collection).filter(Boolean))];
    const allCollections = new Set([...propertyCollections, ...inventoryCollections]);
    
    // Add default collections if none exist
    if (allCollections.size === 0) {
      ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'].forEach(col => 
        allCollections.add(col)
      );
    }
    
    return Array.from(allCollections).sort();
  }, [items, propertyCollections]);

  const filteredItems = useCallback(() => {
    return items.filter(item => {
      // Search filter
      const matchesSearch = !searchQuery ||
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.collection.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.propertyName?.toLowerCase() || '').includes(searchQuery.toLowerCase());

      // Property filter - check both propertyId and propertyName
      const matchesProperty = !filters.property ||
        filters.property === 'all' ||
        item.propertyId === filters.property ||
        item.propertyName === filters.property;

      // Collection filter
      const matchesCollection = !filters.collection ||
        filters.collection === 'all' ||
        item.collection === filters.collection;

      // Stock status filter
      const matchesStockStatus = filters.stockStatus === 'all' ||
        (filters.stockStatus === 'low' && item.quantity < item.minQuantity) ||
        (filters.stockStatus === 'ok' && item.quantity >= item.minQuantity);

      return matchesSearch && matchesProperty && matchesCollection && matchesStockStatus;
    });
  }, [items, searchQuery, filters]);

  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    filteredItems: filteredItems(),
    properties,
    collections
  };
};
