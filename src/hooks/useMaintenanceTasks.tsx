import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { MaintenanceTask, MaintenanceStatus } from '@/components/maintenance/types';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { toast } from 'sonner';

export const useMaintenanceTasks = () => {
  const [tasks, setTasks] = useState<MaintenanceTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const userId = authState.user?.id;

  // Use the impersonation context
  const { isImpersonating, applyImpersonationFilter } = useImpersonation();

  // Track if we've already fetched tasks to prevent duplicate fetches
  const hasFetchedRef = useRef(false);

  const mapDbStatusToMaintenanceStatus = (dbStatus: string): MaintenanceStatus => {
    switch (dbStatus) {
      case 'open': return 'new';
      case 'in_progress': return 'in_progress';
      case 'completed': return 'completed';
      case 'assigned': return 'assigned';
      case 'cancelled': return 'cancelled';
      case 'accepted': return 'accepted';
      case 'rejected': return 'rejected';
      default: return 'new'; // Default fallback
    }
  };

  const mapMaintenanceStatusToDbStatus = (status: MaintenanceStatus): string => {
    switch (status) {
      case 'new': return 'open';
      case 'in_progress': return 'in_progress';
      case 'completed': return 'completed';
      case 'assigned': return 'assigned';
      case 'cancelled': return 'cancelled';
      case 'accepted': return 'accepted';
      case 'rejected': return 'rejected';
      default: return 'open';
    }
  };

  // Note: We're not using this ref to prevent automatic data loading anymore

  const fetchTasks = useCallback(async () => {
    console.log('[useMaintenanceTasks] fetchTasks called');

    // Skip if no user ID
    if (!userId) {
      console.log('[useMaintenanceTasks] No user ID, skipping fetch');
      return;
    }

    console.log('[useMaintenanceTasks] Proceeding with fetch');

    // Add a delay for production to ensure auth is fully initialized
    if (process.env.NODE_ENV === 'production') {
      console.log('[useMaintenanceTasks] Production environment detected, adding delay for auth initialization');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // First, check if we can access properties - this is often a prerequisite
    try {
      console.log('[useMaintenanceTasks] Checking if properties are accessible first');
      const { data: propertiesData, error: propertiesError } = await supabase
        .from('properties')
        .select('count()');

      if (propertiesError) {
        console.error('[useMaintenanceTasks] Error accessing properties:', propertiesError);
      } else {
        console.log('[useMaintenanceTasks] Properties access check result:', propertiesData);
      }
    } catch (propError) {
      console.error('[useMaintenanceTasks] Exception checking properties access:', propError);
    }

    try {
      // Store current tasks to prevent flash/disappear
      const currentTasks = [...tasks];
      console.log('[useMaintenanceTasks] Current tasks before loading:', currentTasks.length);

      setLoading(true);
      setError(null);

      // If we have existing tasks, don't clear them during loading
      if (currentTasks.length > 0) {
        console.log('[useMaintenanceTasks] Keeping existing tasks during loading');
      }

      console.log('Fetching maintenance tasks for user:', userId);
      console.log('[useMaintenanceTasks] User role:', authState?.profile?.role);

      // DIRECT APPROACH: Fetch all tasks for the user's team properties
      // This SQL query is equivalent to:
      // SELECT mt.* FROM maintenance_tasks mt
      // WHERE mt.property_id IN (
      //   SELECT tp.property_id FROM team_properties tp
      //   JOIN team_members tm ON tp.team_id = tm.team_id
      //   WHERE tm.user_id = '1d842b24-1e25-4bf9-aa65-6f59c0f831fb' AND tm.status = 'active'
      // ) OR mt.user_id = '1d842b24-1e25-4bf9-aa65-6f59c0f831fb'

      // First, get all team memberships
      const { data: teamMemberships, error: teamMembershipError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (teamMembershipError) {
        console.error('[useMaintenanceTasks] Error fetching team memberships:', teamMembershipError);
      }

      console.log('[useMaintenanceTasks] Team memberships:', teamMemberships);

      let allTasks: any[] = [];

      // Apply different filters based on user role
      if (isImpersonating) {
        // When impersonating, only show tasks for the impersonated user
        console.log('Impersonating user, showing only their tasks');
        const { data, error } = await applyImpersonationFilter(supabase.from('maintenance_tasks').select('*'));
        if (error) throw error;
        allTasks = data || [];
      } else if (authState?.profile?.is_super_admin || authState?.profile?.role === 'admin') {
        // Admins see all tasks
        console.log('Admin user, showing all tasks');
        const { data, error } = await supabase.from('maintenance_tasks').select('*');
        if (error) throw error;
        allTasks = data || [];
      } else if (authState?.profile?.role === 'service_provider') {
        // Service providers see tasks they created or are assigned to
        const { data: providerData } = await supabase
          .from('service_providers')
          .select('email')
          .eq('id', userId)
          .single();

        const providerEmail = providerData?.email;
        console.log('Service provider email:', providerEmail);

        let query = supabase.from('maintenance_tasks').select('*');
        if (providerEmail) {
          query = query.or(`user_id.eq.${userId},provider_id.eq.${userId},provider_email.eq.${providerEmail}`);
        } else {
          query = query.or(`user_id.eq.${userId},provider_id.eq.${userId}`);
        }

        const { data, error } = await query;
        if (error) throw error;
        allTasks = data || [];
      } else if (authState?.profile?.role === 'staff' || teamMemberships && teamMemberships.length > 0) {
        // User is a staff member or team member - get tasks for team properties
        const teamIds = teamMemberships.map((tm: { team_id: string }) => tm.team_id);
        console.log('[useMaintenanceTasks] User team IDs:', teamIds);
        console.log('[useMaintenanceTasks] User role:', authState?.profile?.role);

        // For staff members in production, try the Edge Function first
        if (authState?.profile?.role === 'staff' && process.env.NODE_ENV === 'production' && teamIds.length > 0) {
          console.log('[useMaintenanceTasks] Staff member in production, trying Edge Function first');
          try {
            const teamId = teamIds[0];
            const { data: teamEdgeData, error: teamEdgeError } = await supabase.functions.invoke('get-team-maintenance-tasks', {
              body: { userId, teamId }
            });

            if (teamEdgeError) {
              console.error('[useMaintenanceTasks] Team Edge function error:', teamEdgeError);
            } else if (teamEdgeData && teamEdgeData.length > 0) {
              console.log(`[useMaintenanceTasks] Team Edge function found ${teamEdgeData.length} tasks`);
              allTasks = teamEdgeData;
              // Skip the rest of the team properties logic by returning early
              return allTasks;
            }
          } catch (edgeErr) {
            console.error('[useMaintenanceTasks] Error calling team Edge function:', edgeErr);
          }
        }

        // Get all property IDs for these teams
        const { data: teamPropertiesData, error: teamPropertiesError } = await supabase
          .from('team_properties')
          .select('property_id')
          .in('team_id', teamIds);

        if (teamPropertiesError) {
          console.error('[useMaintenanceTasks] Error fetching team properties:', teamPropertiesError);
          // Fallback to just showing tasks created by this user
          const { data, error } = await supabase.from('maintenance_tasks').select('*').eq('user_id', userId);
          if (error) throw error;
          allTasks = data || [];
        } else if (teamPropertiesData && teamPropertiesData.length > 0) {
          const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
          console.log('[useMaintenanceTasks] Team property IDs:', propertyIds);

          // IMPORTANT: For team members, we need to query by property_id directly
          // This is because maintenance tasks might be associated with a different team
          // but for the same properties
          console.log('[useMaintenanceTasks] Using direct property ID query for team member');

          // First approach: Query by property_id directly without team_id filter
          const { data: propertyData, error: propertyError } = await supabase
            .from('maintenance_tasks')
            .select('*')
            .in('property_id', propertyIds);

          if (propertyError) {
            console.error('[useMaintenanceTasks] Error with direct property query:', propertyError);
            // Fallback to regular query
            console.log('[useMaintenanceTasks] Falling back to direct query with OR condition');
            const { data, error } = await supabase
              .from('maintenance_tasks')
              .select('*')
              .or(`property_id.in.(${propertyIds.join(',')}),user_id.eq.${userId}`);

            if (error) {
              console.error('[useMaintenanceTasks] Error with fallback query:', error);
              throw error;
            }

            console.log('[useMaintenanceTasks] Found tasks with fallback query:', data?.length || 0);
            allTasks = data || [];
          } else {
            console.log('[useMaintenanceTasks] Found tasks with direct property query:', propertyData?.length || 0);
            allTasks = propertyData || [];
          }
        } else {
          // No team properties, try using the team member function
          console.log('[useMaintenanceTasks] No team properties found, trying team member function');
          const { data: teamMemberData, error: teamMemberError } = await supabase.rpc('get_maintenance_tasks_for_team_member');

          if (teamMemberError) {
            console.error('[useMaintenanceTasks] Error with team member function:', teamMemberError);
            // Fallback to just showing tasks created by this user
            console.log('[useMaintenanceTasks] Falling back to user-created tasks only');
            const { data, error } = await supabase.from('maintenance_tasks').select('*').eq('user_id', userId);
            if (error) throw error;
            allTasks = data || [];
          } else {
            console.log('[useMaintenanceTasks] Found tasks with team member function:', teamMemberData?.length || 0);
            allTasks = teamMemberData || [];
          }
        }
      } else {
        // Default case - just show tasks they created
        const { data, error } = await supabase.from('maintenance_tasks').select('*').eq('user_id', userId);
        if (error) throw error;
        allTasks = data || [];
      }

      // Sort tasks by creation date, most recent first
      allTasks.sort((a, b) => {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return dateB - dateA; // Descending order
      });

      console.log('[useMaintenanceTasks] Processed tasks:', allTasks.length);
      if (allTasks.length > 0) {
        console.log('[useMaintenanceTasks] First task:', allTasks[0]);
      }

      // Format tasks for the UI
      const formattedTasks: MaintenanceTask[] = allTasks.map((task: any) => {
        return {
          id: task.id,
          title: task.title,
          description: task.description || '',
          propertyId: task.property_id || '',
          propertyName: task.property_name || '',
          status: mapDbStatusToMaintenanceStatus(task.status),
          severity: task.severity as MaintenanceTask['severity'],
          dueDate: task.due_date || 'No due date',
          assignedTo: task.assigned_to || undefined,
          createdAt: task.created_at,
          providerId: task.provider_id || undefined,
          providerEmail: task.provider_email || undefined
        };
      });

      // Only update tasks if we have new data or no existing data
      if (formattedTasks.length > 0 || tasks.length === 0) {
        console.log(`[useMaintenanceTasks] Updating tasks: ${formattedTasks.length} new tasks`);
        setTasks(formattedTasks);
      } else {
        console.log('[useMaintenanceTasks] Not updating tasks - would replace with empty array');
      }

      // Mark that we've successfully fetched tasks
      hasFetchedRef.current = true;
    } catch (error: any) {
      console.error('Error fetching maintenance tasks:', error);
      setError(`Failed to load tasks: ${error.message}`);
      toast.error('Failed to load maintenance tasks');
    } finally {
      setLoading(false);
    }
  }, [userId, authState?.profile?.role, authState?.profile?.is_super_admin, isImpersonating]);

  // Fetch tasks when the component mounts and userId is available
  // Also refetch when team membership or permissions change
  useEffect(() => {
    if (userId) {
      console.log('[useMaintenanceTasks] User ID or team data changed, fetching tasks...');
      // Reset the hasFetched flag to ensure we always fetch on dependency changes
      hasFetchedRef.current = false;

      // Store current tasks to prevent flash/disappear
      const currentTasks = [...tasks];
      console.log('[useMaintenanceTasks] Stored current tasks before fetch:', currentTasks.length);

      // Immediately fetch tasks - don't wait for any checks
      fetchTasks();

      // For production, we need to be more aggressive with fetching
      // Try multiple approaches to ensure we get the data
      const fetchWithMultipleApproaches = async () => {
        // In production, add a delay to ensure auth is fully initialized
        if (process.env.NODE_ENV === 'production') {
          console.log('[useMaintenanceTasks] Production environment, adding delay before multiple approaches');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // If we lost our tasks during loading, restore them temporarily
        if (tasks.length === 0 && currentTasks.length > 0) {
          console.log('[useMaintenanceTasks] Tasks disappeared during loading, restoring previous tasks');
          setTasks(currentTasks);
        }
        try {
          // Approach 1: Direct query
          console.log('[useMaintenanceTasks] Trying direct query for maintenance tasks...');
          const { data: directData, error: directError } = await supabase
            .from('maintenance_tasks')
            .select('*');

          if (directError) {
            console.error('[useMaintenanceTasks] Direct query error:', directError);
          } else if (directData && directData.length > 0 && tasks.length === 0) {
            console.log('[useMaintenanceTasks] Got tasks from direct query, updating state');
            // Map the tasks to the correct format
            const formattedTasks = directData.map((task: any) => ({
              id: task.id,
              title: task.title,
              description: task.description || '',
              propertyId: task.property_id || '',
              propertyName: task.property_name || '',
              status: mapDbStatusToMaintenanceStatus(task.status),
              severity: task.severity as MaintenanceTask['severity'],
              dueDate: task.due_date || 'No due date',
              assignedTo: task.assigned_to || undefined,
              createdAt: task.created_at,
              providerId: task.provider_id || undefined,
              providerEmail: task.provider_email || undefined
            }));
            setTasks(formattedTasks);
            return; // Success, no need to try other approaches
          }

          // Approach 2: Property-based query for team members
          console.log('[useMaintenanceTasks] Trying property-based query for team members...');

          // First get team memberships
          const { data: teamMemberships, error: teamMembershipError } = await supabase
            .from('team_members')
            .select('team_id')
            .eq('user_id', userId)
            .eq('status', 'active');

          if (teamMembershipError) {
            console.error('[useMaintenanceTasks] Error fetching team memberships:', teamMembershipError);
          } else if (teamMemberships && teamMemberships.length > 0) {
            const teamIds = teamMemberships.map((tm: any) => tm.team_id);
            console.log('[useMaintenanceTasks] User team IDs:', teamIds);

            // Get properties for these teams
            const { data: teamProperties, error: teamPropertiesError } = await supabase
              .from('team_properties')
              .select('property_id')
              .in('team_id', teamIds);

            if (teamPropertiesError) {
              console.error('[useMaintenanceTasks] Error fetching team properties:', teamPropertiesError);
            } else if (teamProperties && teamProperties.length > 0) {
              const propertyIds = teamProperties.map((tp: any) => tp.property_id);
              console.log('[useMaintenanceTasks] Team property IDs:', propertyIds);

              // Query tasks by property_id directly
              const { data: propertyTasks, error: propertyTasksError } = await supabase
                .from('maintenance_tasks')
                .select('*')
                .in('property_id', propertyIds);

              if (propertyTasksError) {
                console.error('[useMaintenanceTasks] Error fetching property tasks:', propertyTasksError);
              } else if (propertyTasks && propertyTasks.length > 0 && tasks.length === 0) {
                console.log('[useMaintenanceTasks] Got tasks from property query, updating state');
                // Map the tasks to the correct format
                const formattedTasks = propertyTasks.map((task: any) => ({
                  id: task.id,
                  title: task.title,
                  description: task.description || '',
                  propertyId: task.property_id || '',
                  propertyName: task.property_name || '',
                  status: mapDbStatusToMaintenanceStatus(task.status),
                  severity: task.severity as MaintenanceTask['severity'],
                  dueDate: task.due_date || 'No due date',
                  assignedTo: task.assigned_to || undefined,
                  createdAt: task.created_at,
                  providerId: task.provider_id || undefined,
                  providerEmail: task.provider_email || undefined
                }));
                setTasks(formattedTasks);
                return; // Success, no need to try other approaches
              }
            }
          }

          // Approach 3: User-created tasks only
          console.log('[useMaintenanceTasks] Trying user-created tasks only...');
          const { data: userTasksData, error: userTasksError } = await supabase
            .from('maintenance_tasks')
            .select('*')
            .eq('user_id', userId);

          if (userTasksError) {
            console.error('[useMaintenanceTasks] User tasks query error:', userTasksError);
          } else if (userTasksData && userTasksData.length > 0 && tasks.length === 0) {
            console.log('[useMaintenanceTasks] Got user-created tasks, updating state');
            // Map the tasks to the correct format
            const formattedTasks = userTasksData.map((task: any) => ({
              id: task.id,
              title: task.title,
              description: task.description || '',
              propertyId: task.property_id || '',
              propertyName: task.property_name || '',
              status: mapDbStatusToMaintenanceStatus(task.status),
              severity: task.severity as MaintenanceTask['severity'],
              dueDate: task.due_date || 'No due date',
              assignedTo: task.assigned_to || undefined,
              createdAt: task.created_at,
              providerId: task.provider_id || undefined,
              providerEmail: task.provider_email || undefined
            }));
            setTasks(formattedTasks);
          }
        } catch (err) {
          console.error('[useMaintenanceTasks] Error with multiple approaches:', err);
        }
      };

      // Execute the multiple approaches function
      fetchWithMultipleApproaches();

      // In production, try the Edge Function approach after a delay
      if (process.env.NODE_ENV === 'production') {
        setTimeout(async () => {
          console.log('[useMaintenanceTasks] Production environment, trying Edge Function approach');
          try {
            // First try the team-specific Edge Function for staff members
            if (authState?.profile?.role === 'staff') {
              console.log('[useMaintenanceTasks] User is staff, trying team-specific Edge Function');

              // Get team memberships
              const { data: teamMemberships } = await supabase
                .from('team_members')
                .select('team_id')
                .eq('user_id', userId)
                .eq('status', 'active');

              if (teamMemberships && teamMemberships.length > 0) {
                const teamId = teamMemberships[0].team_id;
                console.log(`[useMaintenanceTasks] User is member of team ${teamId}, using team-specific Edge Function`);

                const { data: teamEdgeData, error: teamEdgeError } = await supabase.functions.invoke('get-team-maintenance-tasks', {
                  body: { userId, teamId }
                });

                if (teamEdgeError) {
                  console.error('[useMaintenanceTasks] Team Edge function error:', teamEdgeError);
                } else if (teamEdgeData && teamEdgeData.length > 0) {
                  console.log(`[useMaintenanceTasks] Team Edge function found ${teamEdgeData.length} tasks`);

                  // Map the tasks to the correct format
                  const formattedTasks = teamEdgeData.map((task: any) => ({
                    id: task.id,
                    title: task.title,
                    description: task.description || '',
                    propertyId: task.property_id || '',
                    propertyName: task.property_name || '',
                    status: mapDbStatusToMaintenanceStatus(task.status),
                    severity: task.severity as MaintenanceTask['severity'],
                    dueDate: task.due_date || 'No due date',
                    assignedTo: task.assigned_to || undefined,
                    createdAt: task.created_at,
                    providerId: task.provider_id || undefined,
                    providerEmail: task.provider_email || undefined
                  }));

                  // Only update if we found tasks
                  if (formattedTasks.length > 0) {
                    console.log('[useMaintenanceTasks] Updating tasks with team Edge function data');
                    setTasks(formattedTasks);
                    return; // Success, no need to try other approaches
                  }
                }
              }
            }

            // Fallback to the general Edge Function
            const { data: edgeData, error: edgeError } = await supabase.functions.invoke('get-maintenance-tasks', {
              body: { userId }
            });

            if (edgeError) {
              console.error('[useMaintenanceTasks] Edge function error:', edgeError);
            } else if (edgeData && edgeData.length > 0) {
              console.log(`[useMaintenanceTasks] Edge function found ${edgeData.length} tasks`);

              // Map the tasks to the correct format
              const formattedTasks = edgeData.map((task: any) => ({
                id: task.id,
                title: task.title,
                description: task.description || '',
                propertyId: task.property_id || '',
                propertyName: task.property_name || '',
                status: mapDbStatusToMaintenanceStatus(task.status),
                severity: task.severity as MaintenanceTask['severity'],
                dueDate: task.due_date || 'No due date',
                assignedTo: task.assigned_to || undefined,
                createdAt: task.created_at,
                providerId: task.provider_id || undefined,
                providerEmail: task.provider_email || undefined
              }));

              // Only update if we found tasks
              if (formattedTasks.length > 0) {
                console.log('[useMaintenanceTasks] Updating tasks with Edge function data');
                setTasks(formattedTasks);
              }
            }
          } catch (edgeErr) {
            console.error('[useMaintenanceTasks] Error calling edge function:', edgeErr);
          }
        }, 3000); // Reduced delay to 3 seconds
      }
    }
  }, [userId, fetchTasks, authState?.profile?.role, tasks.length]); // Include role and tasks.length to ensure it's called when role changes

  const addTask = async (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>) => {
    if (!userId) return false;

    try {
      console.log('Adding task in useMaintenanceTasks:', task);
      console.log('User ID:', userId);

      // Determine team_id based on property_id if available
      let teamId = null;
      if (task.propertyId) {
        const { data: teamPropertyData, error: teamPropertyError } = await supabase
          .from('team_properties')
          .select('team_id')
          .eq('property_id', task.propertyId)
          .limit(1);

        if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
          teamId = teamPropertyData[0].team_id;
          console.log(`[useMaintenanceTasks] Found team_id ${teamId} for property ${task.propertyId}`);
        }
      }

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: 'open',
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        user_id: userId,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        team_id: teamId
      };

      console.log('Sending to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .insert(dbTask)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Supabase response:', data);

      if (data && data.length > 0) {
        const newTask: MaintenanceTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description || '',
          propertyId: data[0].property_id || '',
          propertyName: data[0].property_name,
          status: mapDbStatusToMaintenanceStatus(data[0].status),
          severity: data[0].severity as MaintenanceTask['severity'],
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to || undefined,
          createdAt: data[0].created_at,
          providerId: data[0].provider_id || undefined,
          providerEmail: data[0].provider_email || undefined
        };

        setTasks(prev => [newTask, ...prev]);
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Error adding task:', error);
      toast.error(`Failed to add task: ${error.message}`);
      return false;
    }
  };

  const updateTask = async (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>) => {
    if (!userId) return false;

    try {
      console.log('Updating task in useMaintenanceTasks:', task);

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: mapMaintenanceStatusToDbStatus(task.status),
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        updated_at: new Date().toISOString()
      };

      console.log('Sending update to Supabase:', dbTask);

      // First check if user is allowed to update this task (creator or assignee)
      const { data: taskData, error: taskError } = await supabase
        .from('maintenance_tasks')
        .select('user_id, assigned_to')
        .eq('id', taskId)
        .single();

      if (taskError) {
        console.error('Error fetching task details:', taskError);
        throw taskError;
      }

      // Allow updates if user is creator, task is assigned to them, or user is admin/property manager
      const isAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';
      const isPropertyManager = authState?.profile?.role === 'property_manager';
      const isServiceProvider = authState?.profile?.role === 'service_provider';
      const isCreator = taskData.user_id === userId;
      // Check if assigned_to is a UUID that matches userId, or a string with the user's name
      const isAssignee =
        (typeof taskData.assigned_to === 'string' && taskData.assigned_to.includes(authState?.profile?.first_name || '')) ||
        taskData.assigned_to === userId;

      // Check if this task is assigned to this service provider
      let isProviderForTask = false;
      if (isServiceProvider) {
        // Get the provider's email
        const { data: providerData } = await supabase
          .from('service_providers')
          .select('email')
          .eq('id', userId)
          .single();

        const providerEmail = providerData?.email;

        // Check if this service provider is assigned to this task
        isProviderForTask =
          (taskData.provider_id === userId) ||
          (providerEmail && taskData.provider_email === providerEmail);
      }

      if (!isAdmin && !isPropertyManager && !isCreator && !isAssignee && !isProviderForTask) {
        throw new Error('You do not have permission to update this task');
      }

      // Proceed with update if permissions check passed
      const { data, error } = await supabase
        .from('maintenance_tasks')
        .update(dbTask)
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Supabase update response:', data);

      if (data && data.length > 0) {
        const updatedTask: MaintenanceTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description || '',
          propertyId: data[0].property_id || '',
          propertyName: data[0].property_name,
          status: mapDbStatusToMaintenanceStatus(data[0].status),
          severity: data[0].severity as MaintenanceTask['severity'],
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to || undefined,
          createdAt: data[0].created_at,
          providerId: data[0].provider_id || undefined,
          providerEmail: data[0].provider_email || undefined
        };

        setTasks(prev =>
          prev.map(t => t.id === taskId ? updatedTask : t)
        );

        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Error updating task:', error);
      toast.error(`Failed to update task: ${error.message}`);
      return false;
    }
  };

  const updateTaskStatus = async (taskId: string, newStatus: MaintenanceStatus) => {
    if (!userId) return false;

    try {
      const dbStatus = mapMaintenanceStatusToDbStatus(newStatus);
      console.log(`Updating task ${taskId} status to ${newStatus} (DB: ${dbStatus})`);

      // First check if user is allowed to update this task (creator or assignee)
      const { data: taskData, error: taskError } = await supabase
        .from('maintenance_tasks')
        .select('user_id, assigned_to')
        .eq('id', taskId)
        .single();

      if (taskError) {
        console.error('Error fetching task details:', taskError);
        throw taskError;
      }

      // Allow updates if user is creator, task is assigned to them, or user is admin/property manager
      const isAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';
      const isPropertyManager = authState?.profile?.role === 'property_manager';
      const isServiceProvider = authState?.profile?.role === 'service_provider';
      const isCreator = taskData.user_id === userId;
      // Check if assigned_to is a UUID that matches userId, or a string with the user's name
      const isAssignee =
        (typeof taskData.assigned_to === 'string' && taskData.assigned_to.includes(authState?.profile?.first_name || '')) ||
        taskData.assigned_to === userId;

      // Check if this task is assigned to this service provider
      let isProviderForTask = false;
      if (isServiceProvider) {
        // Get the task details to check provider_id and provider_email
        const { data: fullTaskData, error: fullTaskError } = await supabase
          .from('maintenance_tasks')
          .select('provider_id, provider_email')
          .eq('id', taskId)
          .single();

        if (!fullTaskError && fullTaskData) {
          // Get the provider's email
          const { data: providerData } = await supabase
            .from('service_providers')
            .select('email')
            .eq('id', userId)
            .single();

          const providerEmail = providerData?.email;

          // Check if this service provider is assigned to this task
          isProviderForTask =
            (fullTaskData.provider_id === userId) ||
            (providerEmail && fullTaskData.provider_email === providerEmail);
        }
      }

      // Service providers can only update status to 'in_progress' or 'completed'
      if (isServiceProvider && isProviderForTask) {
        // Only allow specific status changes for service providers
        if (newStatus !== 'in_progress' && newStatus !== 'completed') {
          throw new Error('Service providers can only mark tasks as in progress or completed');
        }
      } else if (!isAdmin && !isPropertyManager && !isCreator && !isAssignee && !isProviderForTask) {
        throw new Error('You do not have permission to update this task');
      }

      // Proceed with status update if permissions check passed
      const { error } = await supabase
        .from('maintenance_tasks')
        .update({ status: dbStatus, updated_at: new Date().toISOString() })
        .eq('id', taskId);

      if (error) throw error;

      console.log('Status update successful, refreshing task list');

      setTasks(prev =>
        prev.map(task =>
          task.id === taskId ? { ...task, status: newStatus } : task
        )
      );

      return true;
    } catch (error: any) {
      console.error('Error updating task status:', error);
      toast.error(`Failed to update task: ${error.message}`);
      return false;
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!userId) return false;

    try {
      // First check if user is allowed to delete this task
      const { data: taskData, error: taskError } = await supabase
        .from('maintenance_tasks')
        .select('user_id, assigned_to, property_id')
        .eq('id', taskId)
        .single();

      if (taskError) {
        console.error('Error fetching task details:', taskError);
        throw taskError;
      }

      // Only allow deletion by:
      // 1. The task creator
      // 2. Admins
      // 3. Property managers of the team that owns the property
      // 4. Service providers who are assigned to the task
      const isAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';
      const isServiceProvider = authState?.profile?.role === 'service_provider';
      const isCreator = taskData.user_id === userId;

      // Property managers can delete tasks they created
      let canDeleteAsManager = authState?.profile?.role === 'property_manager';

      // Check if this task is assigned to this service provider
      let isProviderForTask = false;
      if (isServiceProvider) {
        // Get the provider's email
        const { data: providerData } = await supabase
          .from('service_providers')
          .select('email')
          .eq('id', userId)
          .single();

        const providerEmail = providerData?.email;

        // Check if this service provider is assigned to this task
        isProviderForTask =
          (taskData.provider_id === userId) ||
          (providerEmail && taskData.provider_email === providerEmail);
      }

      if (!isAdmin && !isCreator && !canDeleteAsManager && !isProviderForTask) {
        throw new Error('You do not have permission to delete this task');
      }

      // Proceed with deletion if permissions check passed
      const { error } = await supabase
        .from('maintenance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      setTasks(prev => prev.filter(task => task.id !== taskId));

      toast.success('Task deleted successfully');
      return true;
    } catch (error: any) {
      console.error('Error deleting task:', error);
      toast.error(`Failed to delete task: ${error.message}`);
      return false;
    }
  };

  const refreshTasks = useCallback(() => {
    console.log('[useMaintenanceTasks] Refreshing tasks...');
    hasFetchedRef.current = false;
    fetchTasks();
  }, [fetchTasks]);

  // Log the tasks before returning them
  useEffect(() => {
    console.log('[useMaintenanceTasks] Current tasks:', tasks);
  }, [tasks]);

  return {
    tasks,
    loading,
    error,
    fetchTasks,
    addTask: useCallback(addTask, [userId]),
    updateTask: useCallback(updateTask, [userId]),
    updateTaskStatus,
    deleteTask: useCallback(deleteTask, [userId]),
    refreshTasks
  };
};
