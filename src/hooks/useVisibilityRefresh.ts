import { useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';

/**
 * This hook listens for auth-settled-on-focus events from AuthContext
 * and refreshes data queries when the session is refreshed.
 */
export const useVisibilityRefresh = () => {
  const queryClient = useQueryClient();
  const { authState } = useAuth();

  const refreshData = useCallback(() => {
    if (!authState.isAuthenticated || !authState.user?.id) {
      console.log('[useVisibilityRefresh] User not authenticated or userId not available, skipping refreshData.');
      return;
    }

    // Invalidate queries for relevant data
    const queryKeys = [
      'properties',
      'maintenanceTasks',
      'inventoryItems',
      'damageReports',
      'purchaseOrders',
      'teamsV2',
      'teamMembersV2',
      'teamPropertiesV2',
      'teamInvitationsV2',
      'automationRulesV2',
      'maintenanceTasksV2',
      'propertiesV2',
      'inventoryV2',
      'damageReportsV2',
      'purchaseOrdersV2',
      'dashboardProperties',
      'dashboardMaintenanceTasks',
      'dashboardInventoryItems',
      'dashboardDamages',
      'dashboardPurchaseOrders'
    ];

    console.log('[useVisibilityRefresh] Manually refreshing data for keys:', queryKeys);

    queryKeys.forEach((key) => {
      queryClient.invalidateQueries({
        queryKey: [key],
        refetchType: 'active',
        exact: false,
      });
    });

    // Dispatch event to notify other components that data was refreshed
    window.dispatchEvent(new CustomEvent('stayfu-data-refreshed', {
      detail: {
        source: 'useVisibilityRefresh',
        timestamp: Date.now()
      }
    }));
  }, [queryClient, authState]);

  // Listen for auth-settled-on-focus events from AuthContext
  useEffect(() => {
    const handleAuthSettledOnFocus = () => {
      console.log('[useVisibilityRefresh] Received stayfu-auth-settled-on-focus event, refreshing data...');
      refreshData();
    };

    window.addEventListener('stayfu-auth-settled-on-focus', handleAuthSettledOnFocus);
    
    return () => {
      window.removeEventListener('stayfu-auth-settled-on-focus', handleAuthSettledOnFocus);
    };
  }, [refreshData]);

  // Return the refresh function for manual use
  return { refreshData };
};