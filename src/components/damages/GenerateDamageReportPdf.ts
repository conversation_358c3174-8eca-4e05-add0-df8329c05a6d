import jsPDF from 'jspdf';
import { DamageReport } from '@/types/damages';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner'; // Keep toast for potential future error reporting

// Re-defining interfaces locally as they might differ slightly from main types
// or to avoid complex imports if this file is isolated.
// Ideally, these should align with or import from '@/types/damages'.
interface DamagePhoto {
  id: string;
  file_name: string;
  file_path: string;
  created_at: string;
  caption?: string;
  url?: string; // URL might be pre-generated and passed in
  damage_report_id: string; // This is the correct column name
}

interface DamageNote {
  id: string;
  content: string;
  created_at: string;
  created_by?: string; // Consider fetching user name if needed
  private?: boolean;
  damage_report_id: string; // This is the correct column name
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface DamageInvoice {
  id: string;
  invoice_number?: string;
  total_amount?: number;
  status: string;
  issue_date?: string;
  due_date?: string;
  notes?: string;
  created_at: string;
  provider_name?: string; // Name might be denormalized or fetched separately
  items?: InvoiceItem[]; // Items should ideally be passed in
  damage_report_id?: string;
}

interface Property {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
  notes?: string;
  address?: string;
}

interface GeneratePdfOptions {
  includePhotos: boolean;
  includeNotes: boolean;
  includeInvoices: boolean;
  specificInvoiceId?: string; // Option to generate for a single invoice
}

// Function to get signed URL for photos
const getSignedPhotoUrl = async (filePath: string): Promise<string | undefined> => { // Return undefined instead of null
  try {
    // First try to get a public URL
    const { data: publicData } = await supabase.storage
      .from('damage-photos')
      .getPublicUrl(filePath);

    if (publicData?.publicUrl) {
      console.log('Using public URL for photo:', filePath);
      return publicData.publicUrl;
    }

    // If public URL fails, try signed URL
    console.log('Trying signed URL for photo:', filePath);
    const { data, error } = await supabase.storage
      .from('damage-photos')
      .createSignedUrl(filePath, 3600); // Use createSignedUrl, 1 hour expiry

    if (error) {
      throw error;
    }
    // Return signedUrl (which is string | undefined) or explicitly undefined
    return data?.signedUrl ?? undefined;
  } catch (error) {
    console.error('Error getting photo URL:', error); // Keep essential error logs
    return undefined; // Return undefined on error
  }
};

// Helper function to render professional invoice layout
const renderProfessionalInvoice = (pdf: jsPDF, invoice: DamageInvoice, property: Property | undefined, provider: Provider | undefined, pageWidth: number, pageHeight: number, margin: number, startY: number) => {
  let yPos = startY;
  
  // Draw border around the invoice
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(0.5);
  const borderHeight = Math.min(pageHeight - yPos - margin, 250); // Limit border height
  pdf.rect(margin, yPos, pageWidth - 2 * margin, borderHeight);

  let currentY = yPos + 10;

  // Company/Provider Information (Top Left)
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.text(provider?.name || 'Company Name', margin + 5, currentY);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);
  currentY += 5;

  // Provider address (from notes field)
  if (provider?.notes) {
    const addressLines = provider.notes.split('\n');
    addressLines.forEach(line => {
      if (line.trim()) {
        pdf.text(line.trim(), margin + 5, currentY);
        currentY += 4;
      }
    });
  }

  // Provider contact info
  if (provider?.phone) {
    pdf.text(`Phone: ${provider.phone}`, margin + 5, currentY);
    currentY += 4;
  }

  if (provider?.email) {
    pdf.text(`Email: ${provider.email}`, margin + 5, currentY);
  }

  // INVOICE title (Top Right)
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(24);
  pdf.setTextColor(150, 150, 150); // Gray color
  pdf.text('INVOICE', pageWidth - margin - 5, yPos + 20, { align: 'right' });

  // Invoice details box (Top Right)
  pdf.setTextColor(0, 0, 0);
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);

  const boxX = pageWidth - 80;
  const boxY = yPos + 30;
  const boxWidth = 60;
  const boxHeight = 20;

  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(0.3);
  pdf.rect(boxX, boxY, boxWidth, boxHeight);

  // Invoice details headers
  pdf.setFillColor(220, 220, 220);
  pdf.rect(boxX, boxY, boxWidth, 5, 'F');
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(8);
  pdf.text('INVOICE #', boxX + 2, boxY + 3);
  pdf.text('DATE', boxX + 32, boxY + 3);

  // Invoice details values
  pdf.setFont('helvetica', 'normal');
  pdf.text(invoice.invoice_number || 'N/A', boxX + 2, boxY + 8);
  pdf.text(invoice.issue_date ? format(new Date(invoice.issue_date), 'M/d/yyyy') : format(new Date(), 'M/d/yyyy'), boxX + 32, boxY + 8);

  // BILL TO section
  currentY = yPos + 70;
  pdf.setFillColor(220, 220, 220);
  pdf.rect(margin + 5, currentY, 100, 8, 'F');
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);
  pdf.text('BILL TO', margin + 7, currentY + 5);

  currentY += 12;
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);
  
  // Use real property data
  if (property?.name) {
    pdf.text(property.name, margin + 5, currentY);
    currentY += 4;
  }

  if (property?.address) {
    pdf.text(property.address, margin + 5, currentY);
    currentY += 4;
  }

  const cityState = [property?.city, property?.state].filter(Boolean).join(', ');
  if (cityState) {
    pdf.text(cityState, margin + 5, currentY);
    currentY += 4;
  }

  // Items table
  currentY = yPos + 150;

  // Table header
  pdf.setFillColor(220, 220, 220);
  pdf.rect(margin + 5, currentY, pageWidth - 2 * margin - 10, 8, 'F');
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);
  pdf.text('DESCRIPTION', margin + 7, currentY + 5);
  pdf.text('AMOUNT', pageWidth - margin - 7, currentY + 5, { align: 'right' });

  currentY += 12;

  // Invoice items
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);

  if (invoice.items && invoice.items.length > 0) {
    invoice.items.forEach(item => {
      pdf.text(item.description || 'No description', margin + 7, currentY);
      pdf.text((item.amount || 0).toFixed(2), pageWidth - margin - 7, currentY, { align: 'right' });
      currentY += 6;
    });
  }

  // Add space for more items
  currentY = Math.max(currentY, yPos + 210);

  // Thank you message
  pdf.setFont('helvetica', 'italic');
  pdf.setFontSize(9);
  pdf.text('Thank you for your business!', margin + 7, currentY);

  // Total section
  const totalBoxY = currentY + 5;
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(0.3);
  pdf.rect(pageWidth - 80, totalBoxY, 60, 12);

  pdf.setFillColor(220, 220, 220);
  pdf.rect(pageWidth - 80, totalBoxY, 30, 12, 'F');

  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(10);
  pdf.text('TOTAL', pageWidth - 75, totalBoxY + 7);
  pdf.text('$', pageWidth - 45, totalBoxY + 7);
  pdf.text((invoice.total_amount || 0).toFixed(2), pageWidth - 25, totalBoxY + 7, { align: 'right' });

  // Footer message
  currentY = totalBoxY + 30;
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(8);
  pdf.text('If you have any questions about this invoice, please contact', pageWidth / 2, currentY, { align: 'center' });
  currentY += 4;

  // Use actual provider contact info
  const contactInfo: string[] = [];
  if (provider?.name) contactInfo.push(provider.name);
  if (provider?.phone) contactInfo.push(provider.phone);
  if (provider?.email) contactInfo.push(provider.email);

  const contactText = contactInfo.length > 0
    ? contactInfo.join(', ')
    : '[Name, Phone, <EMAIL>]';

  pdf.text(contactText, pageWidth / 2, currentY, { align: 'center' });

  return currentY + 10; // Return the new Y position
};

export const generateDamageReportPdf = async (
  report: DamageReport,
  photos: DamagePhoto[], // Expect photos array to be passed in
  notes: DamageNote[],   // Expect notes array to be passed in
  invoices: DamageInvoice[], // Expect invoices array (potentially with items) to be passed in
  property?: Property,
  provider?: Provider,
  options: GeneratePdfOptions = {
    includePhotos: true,
    includeNotes: true,
    includeInvoices: true
  }
) => {

  // Fetch invoice items if needed (ideally items should be passed with invoices)
  if (options.includeInvoices && invoices.length > 0) {
    const invoicesToProcess = options.specificInvoiceId
      ? invoices.filter(inv => inv.id === options.specificInvoiceId)
      : invoices;

    for (const invoice of invoicesToProcess) {
      // Only fetch if items are missing
      if (!invoice.items || invoice.items.length === 0) {
        try {
          const { data: itemsData, error: itemsError } = await supabase
            .from('invoice_items')
            .select('*')
            .eq('invoice_id', invoice.id)
            .order('created_at', { ascending: true });

          if (itemsError) {
            console.error(`Error fetching items for invoice ${invoice.id}:`, itemsError);
            continue; // Skip items for this invoice on error
          }
          invoice.items = itemsData as InvoiceItem[];
        } catch (error) {
          console.error(`Error processing items for invoice ${invoice.id}:`, error);
        }
      }
    }
  }

  // Pre-process photo URLs if necessary (generate signed URLs if not already present)
  const photosWithUrls: DamagePhoto[] = [];
  if (options.includePhotos && photos.length > 0) {
    console.log('Processing photos for PDF:', photos);

    for (const photo of photos) {
      // First try to use the existing URL
      let url = photo.url;

      // If no URL but we have a file_path, try to get a public URL
      if ((!url || url === 'null' || url === 'undefined') && photo.file_path) {
        console.log('Getting public URL for photo:', photo.file_path);
        try {
          const { data: publicData } = await supabase.storage
            .from('damage-photos')
            .getPublicUrl(photo.file_path);

          if (publicData?.publicUrl) {
            url = publicData.publicUrl;
            console.log('Got public URL:', url);
          }
        } catch (error) {
          console.error('Error getting public URL:', error);
        }
      }

      // If still no URL, try to get a signed URL
      if ((!url || url === 'null' || url === 'undefined') && photo.file_path) {
        console.log('Getting signed URL for photo:', photo.file_path);
        url = await getSignedPhotoUrl(photo.file_path);
      }

      if (url && url !== 'null' && url !== 'undefined') {
        console.log('Adding photo with URL:', url);
        photosWithUrls.push({ ...photo, url }); // Add photo with URL
      } else {
        console.error(`Could not get URL for photo: ${photo.file_name}`); // Log if URL failed
      }
    }

    console.log(`Processed ${photosWithUrls.length} photos with URLs for PDF`);
  }


  // --- PDF Generation Logic ---
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  let yPos = 20; // Initial Y position
  const margin = 15; // Page margin
  const contentWidth = pageWidth - 2 * margin;

  // Helper function to add text and manage Y position
  const addText = (text: string, fontSize = 10, isBold = false, xOffset = 0) => {
    if (yPos > pageHeight - margin) { // Check for page break before adding text
      pdf.addPage();
      yPos = margin;
    }
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', isBold ? 'bold' : 'normal');
    pdf.text(text, margin + xOffset, yPos);
    yPos += (fontSize / 2.5); // Adjust line spacing based on font size
  };

  // Helper function for wrapped text
  const addWrappedText = (text: string | null | undefined, fontSize = 10, lineHeight = 5) => {
    if (!text) return;
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', 'normal');

    const lines = pdf.splitTextToSize(text, contentWidth);
    lines.forEach((line: string) => {
      if (yPos > pageHeight - margin - lineHeight) { // Check space before adding line
        pdf.addPage();
        yPos = margin;
      }
      pdf.text(line, margin, yPos);
      yPos += lineHeight;
    });
  };

  // Helper to check for page break
  const checkNewPage = (requiredSpace: number) => {
    if (yPos + requiredSpace > pageHeight - margin) {
      pdf.addPage();
      yPos = margin;
      return true;
    }
    return false;
  };

  // Helper for section headers
  const addSectionHeader = (text: string) => {
    if (checkNewPage(15)) { // Add extra space if new page
        yPos += 5;
    } else {
        yPos += 8; // Space before header
    }
    addText(text, 14, true);
    pdf.setDrawColor(200, 200, 200);
    pdf.line(margin, yPos, pageWidth - margin, yPos); // Line below header
    yPos += 6; // Space after header line
  };

  // --- PDF Content ---

  // Filter invoices if a specific one is requested
  const filteredInvoices = options.specificInvoiceId
    ? invoices.filter(inv => inv.id === options.specificInvoiceId)
    : invoices;

  // Main Title
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  const mainTitle = options.specificInvoiceId && filteredInvoices.length > 0
    ? `Invoice #${filteredInvoices[0].invoice_number || 'Details'}`
    : 'Damage Report';
  pdf.text(mainTitle, pageWidth / 2, yPos, { align: 'center' });
  yPos += 12;

  // Report Details
  addText(`Report ID: ${report.id}`, 9);
  addText(`Title: ${report.title}`, 11, true);
  addText(`Status: ${report.status?.toUpperCase() || 'N/A'}`, 9);
  addText(`Created: ${format(new Date(report.created_at), 'PPP')}`, 9);
  addText(`Last Updated: ${format(new Date(report.updated_at), 'PPP')}`, 9);
  if (report.platform) {
    addText(`Platform: ${report.platform}`, 9);
  }
  yPos += 5;

  // Property Info
  if (property) {
    addSectionHeader('Property Information');
    addText(`Name: ${property.name}`, 10, true);
    if (property.address) {
      addText(`Address: ${property.address}`, 9);
    }
    if (property.city && property.state) {
      addText(`Location: ${property.city}, ${property.state}`, 9);
    }
    yPos += 3;
  }

  // Provider Info (Show only if generating full report or if it's the provider for the specific invoice)
  const shouldShowProvider = provider && (!options.specificInvoiceId || (filteredInvoices.length > 0 && report.provider_id === provider.id));
  if (shouldShowProvider) {
    addSectionHeader('Assigned Service Provider');
    addText(`Name: ${provider.name}`, 10, true);
    if (provider.specialty) {
      addText(`Specialty: ${provider.specialty}`, 9);
    }
    if (provider.email) {
      addText(`Email: ${provider.email}`, 9);
    }
    if (provider.phone) {
      addText(`Phone: ${provider.phone}`, 9);
    }
    yPos += 3;
  }

  // Description (Only for full report)
  if (!options.specificInvoiceId) {
    addSectionHeader('Description');
    addWrappedText(report.description);
    yPos += 5;
  }

  // Photos (Only for full report)
  if (options.includePhotos && !options.specificInvoiceId) {
    addSectionHeader('Photos');
    if (photosWithUrls.length > 0) {
      for (let i = 0; i < photosWithUrls.length; i++) {
        const photo = photosWithUrls[i];
        if (checkNewPage(70)) { // Check space for image + caption
           addSectionHeader('Photos (Continued)'); // Add header again if page breaks
        }

        if (photo.url) {
          try {
            // --- Image Loading and Drawing ---
            console.log('Loading image from URL:', photo.url);
            let dataUrl;

            try {
              // First try to get a signed URL if we don't already have one
              let imageUrl = photo.url;

              // If we have a file_path but the URL is failing, try to get a fresh signed URL
              if (photo.file_path && (!imageUrl || imageUrl.includes('undefined'))) {
                console.log('Getting fresh signed URL for:', photo.file_path);
                try {
                  const { data: urlData } = await supabase
                    .storage
                    .from('damage-photos')
                    .createSignedUrl(photo.file_path, 3600);

                  if (urlData?.signedUrl) {
                    imageUrl = urlData.signedUrl;
                    console.log('Got fresh signed URL:', imageUrl);
                  }
                } catch (signedUrlError) {
                  console.error('Error getting signed URL:', signedUrlError);
                }
              }

              // This part is complex and browser-dependent. Using jsPDF's built-in
              // image support is generally preferred if possible, but requires
              // the image data (e.g., Base64). Fetching and converting here.
              const response = await fetch(imageUrl, {
                mode: 'cors',
                cache: 'no-cache',
                headers: {
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                }
              }); // Fetch the image with CORS headers

              if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

              const blob = await response.blob();
              const reader = new FileReader();
              dataUrl = await new Promise<string>((resolve, reject) => {
                  reader.onloadend = () => resolve(reader.result as string);
                  reader.onerror = reject;
                  reader.readAsDataURL(blob);
              });
              // --- End Image Loading ---
              console.log('Successfully loaded image data');
            } catch (fetchError) {
              console.error('Error fetching image:', fetchError);
              throw fetchError; // Re-throw to be caught by the outer try/catch
            }

            const imgProps = pdf.getImageProperties(dataUrl as string);
            const aspectRatio = imgProps.width / imgProps.height;
            const maxWidth = contentWidth; // 100% of content width
            const maxHeight = 120; // Increased max height

            let imgWidth = maxWidth;
            let imgHeight = imgWidth / aspectRatio;

            if (imgHeight > maxHeight) {
              imgHeight = maxHeight;
              imgWidth = imgHeight * aspectRatio;
            }

             if (checkNewPage(imgHeight + 10)) { // Check again after getting dimensions
                 addSectionHeader('Photos (Continued)');
             }

            // Center the image on the page
            const imgXPos = (pageWidth - imgWidth) / 2;
            pdf.addImage(dataUrl, 'JPEG', imgXPos, yPos, imgWidth, imgHeight);
            yPos += imgHeight + 3;

            // Add caption
            if (photo.caption) {
              addWrappedText(photo.caption, 8, 4);
            }
            yPos += 7; // Space after photo/caption

          } catch (imgError) {
            console.error('Error processing image for PDF:', imgError);
            addText(`[Image: ${photo.file_name || 'Unnamed'}] - Failed to load`, 9, false);
            yPos += 5;
          }
        } else {
           addText(`[Photo: ${photo.file_name || 'Unnamed'}] - URL missing`, 9, false);
           yPos += 5;
        }
      }
    } else {
      addText('No photos included.', 9, false);
      yPos += 5;
    }
  }

  // Notes (Only for full report)
  if (options.includeNotes && !options.specificInvoiceId) {
    addSectionHeader('Notes');
    console.log('Processing notes for PDF:', notes);

    // Filter out private notes - only include non-private notes
    const publicNotes = notes.filter(note => !note.private);
    
    if (publicNotes && publicNotes.length > 0) {
      publicNotes.forEach((note, index) => {
        if (checkNewPage(20)) { // Check space for note header + some text
            addSectionHeader('Notes (Continued)');
        }
        const noteDate = format(new Date(note.created_at), 'PPP p');
        const noteAuthor = note.created_by || 'User';

        addText(`Note by ${noteAuthor} on ${noteDate}`, 9, true);
        yPos += 1;
        addWrappedText(note.content, 9, 4.5);
        yPos += 5; // Space after note
      });
    } else {
      addText('No public notes included.', 9, false);
      yPos += 5;
    }
  }

  // Invoices - Always start on a new page to look professional
  if (options.includeInvoices && filteredInvoices.length > 0) {
    // Add a new page for invoices to separate them from other content
    pdf.addPage();
    yPos = margin + 10;

    filteredInvoices.forEach((invoice, index) => {
      // For multiple invoices, start each on a new page
      if (index > 0) {
        pdf.addPage();
        yPos = margin + 10;
      }

      // Use professional invoice template
      yPos = renderProfessionalInvoice(pdf, invoice, property, provider, pageWidth, pageHeight, margin, yPos);

      // Invoice Notes (if any)
      if (invoice.notes) {
        yPos += 10;
        if (checkNewPage(15)) {
          pdf.addPage();
          yPos = margin;
          addSectionHeader('Invoice Notes');
        } else {
          addSectionHeader('Invoice Notes');
        }
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        addWrappedText(invoice.notes, 9, 4.5);
        yPos += 5;
      }
      yPos += 10; // Space after each invoice
    });
  } else if (options.includeInvoices && !options.specificInvoiceId) {
    addSectionHeader('Invoices');
    addText('No invoices included.', 9, false);
    yPos += 5;
  }

  // PDF Footer
  const pageCount = pdf.internal.pages.length; // Get total pages
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  for (let i = 1; i <= pageCount; i++) {
    pdf.setPage(i);
    pdf.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
    pdf.text(`Generated on ${format(new Date(), 'PPP p')}`, margin, pageHeight - 10);
  }

  // Save PDF
  const filename = options.specificInvoiceId && filteredInvoices.length > 0
    ? `Invoice_${filteredInvoices[0].invoice_number || filteredInvoices[0].id.substring(0, 8)}.pdf`
    : `Damage_Report_${report.id.substring(0, 8)}.pdf`;

  pdf.save(filename);
};
