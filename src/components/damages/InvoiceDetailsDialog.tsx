
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { DamageReport, Invoice } from '@/types/damages';
import {
  Download,
  FileText,
  Edit,
  Trash2,
  ChevronRight
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { format } from 'date-fns';

interface InvoiceDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string;
  damageReport: DamageReport;
  onInvoiceUpdated: () => void;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

const InvoiceDetailsDialog: React.FC<InvoiceDetailsDialogProps> = ({
  open,
  onOpenChange,
  invoiceId,
  damageReport,
  onInvoiceUpdated
}) => {
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  useEffect(() => {
    if (open && invoiceId) {
      fetchInvoiceDetails();
    }
  }, [open, invoiceId]);
  
  const fetchInvoiceDetails = async () => {
    if (!invoiceId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Fetch invoice header
      const { data: invoiceData, error: invoiceError } = await supabase
        .from('damage_invoices')
        .select(`
          *,
          maintenance_providers(name, email, phone, specialty, notes)
        `)
        .eq('id', invoiceId)
        .single();
      
      if (invoiceError) throw invoiceError;
      
      // Fetch invoice items
      const { data: itemsData, error: itemsError } = await supabase
        .from('invoice_items')
        .select('*')
        .eq('invoice_id', invoiceId)
        .order('id');
      
      if (itemsError) throw itemsError;
      
      setInvoice(invoiceData);
      setItems(itemsData || []);
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      setError('Failed to load invoice details');
    } finally {
      setLoading(false);
    }
  };
  
  const handleDownload = async () => {
    if (!invoice) return;
    
    try {
      // If the invoice has a file path, try to download it from storage
      if (invoice.file_path) {
        const { data, error } = await supabase.storage
          .from('invoice-files')
          .download(invoice.file_path);
        
        if (error) throw error;
        
        // Create a download link
        const url = URL.createObjectURL(data);
        const a = document.createElement('a');
        a.href = url;
        a.download = invoice.file_name || `invoice-${invoice.invoice_number}.pdf`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success('Invoice downloaded successfully');
      } else {
        // If no file path, generate a PDF
        toast.info('Generating PDF...');
        
        // Fetch real property data
        const { data: propertyData, error: propertyError } = await supabase
          .from('properties')
          .select('id, name, address, city, state, zip')
          .eq('id', damageReport.property_id)
          .single();
        
        if (propertyError) {
          console.error('Error fetching property:', propertyError);
        }

        const property = propertyData || { 
          id: damageReport.property_id || damageReport.id, 
          name: damageReport.property_name || 'Property',
          address: '',
          city: '',
          state: ''
        };
        
        // Import dynamically to reduce initial load time
        const { generateInvoicePdf } = await import('./detail-tabs/invoice/utils/pdfGenerator');
        
        const provider = invoice.provider_id ? {
          id: invoice.provider_id,
          name: invoice.maintenance_providers?.name || 'Unknown Provider',
          email: invoice.maintenance_providers?.email,
          phone: invoice.maintenance_providers?.phone,
          specialty: invoice.maintenance_providers?.specialty,
          notes: invoice.maintenance_providers?.notes || ''
        } : {
          id: 'no-provider',
          name: 'No Provider Assigned',
          email: '',
          phone: '',
          specialty: '',
          notes: ''
        };
        
        await generateInvoicePdf(invoice, damageReport.id, property, provider);
        toast.success('Invoice PDF generated successfully');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error('Failed to download invoice');
    }
  };
  
  const handleDeleteInvoice = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteInvoice = async () => {
    if (!invoice) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('damage_invoices')
        .delete()
        .eq('id', invoice.id);

      if (error) throw error;

      setTimeout(() => {
        toast.success('Invoice deleted successfully');
      }, 0);
      onInvoiceUpdated(); // Refresh the invoice list
      onOpenChange(false); // Close the dialog
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting invoice:', error);
      setTimeout(() => {
        toast.error('Failed to delete invoice');
      }, 0);
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDeleteInvoice = () => {
    setIsDeleteDialogOpen(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-full w-full h-full overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {loading ? 'Loading Invoice Details...' : 
              `Invoice #${invoice?.invoice_number || 'N/A'}`}
          </DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : error ? (
          <div className="py-6 text-center">
            <FileText className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <p className="text-destructive">{error}</p>
          </div>
        ) : invoice ? (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <p className={`font-semibold ${
                  invoice.status === 'paid' ? 'text-green-600 dark:text-green-400' :
                  invoice.status === 'pending' ? 'text-amber-600 dark:text-amber-400' :
                  'text-muted-foreground'
                }`}>
                  {invoice.status?.toUpperCase() || 'DRAFT'}
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Total Amount</h3>
                <p className="font-semibold">${invoice.total_amount?.toFixed(2) || '0.00'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Issue Date</h3>
                <p>{invoice.issue_date ? format(new Date(invoice.issue_date), 'MMM d, yyyy') : 'N/A'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Due Date</h3>
                <p>{invoice.due_date ? format(new Date(invoice.due_date), 'MMM d, yyyy') : 'N/A'}</p>
              </div>
              
              <div className="col-span-2">
                <h3 className="text-sm font-medium text-muted-foreground">Service Provider</h3>
                <p>{invoice.maintenance_providers?.name || 'None assigned'}</p>
                {invoice.maintenance_providers?.email && (
                  <p className="text-sm text-muted-foreground">{invoice.maintenance_providers.email}</p>
                )}
              </div>
            </div>
            
            {items.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">Invoice Items</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                        <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {items.map(item => (
                        <tr key={item.id}>
                          <td className="px-4 py-2 text-sm">{item.description}</td>
                          <td className="px-4 py-2 text-sm text-right">{item.quantity}</td>
                          <td className="px-4 py-2 text-sm text-right">${item.unit_price.toFixed(2)}</td>
                          <td className="px-4 py-2 text-sm text-right font-medium">${item.amount.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-gray-50">
                      <tr>
                        <td colSpan={3} className="px-4 py-2 text-sm font-medium text-right">Total:</td>
                        <td className="px-4 py-2 text-sm font-bold text-right">${invoice.total_amount?.toFixed(2) || '0.00'}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            )}
            
            {invoice.notes && (
              <div>
                <h3 className="text-sm font-medium mb-1">Notes</h3>
                <p className="text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded-md">
                  {invoice.notes}
                </p>
              </div>
            )}
            
            {invoice.file_path && (
              <div className="bg-blue-50 p-3 rounded-md flex items-center justify-between">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-blue-600 mr-2" />
                  <div>
                    <p className="text-sm font-medium">Uploaded PDF Available</p>
                    <p className="text-xs text-muted-foreground">{invoice.file_name}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="py-6 text-center">
            <p>No invoice data found</p>
          </div>
        )}
        
        <DialogFooter className="flex justify-between items-center">
          <div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteInvoice}
              disabled={!invoice}
              className="mr-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Invoice
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
            <Button onClick={handleDownload} disabled={!invoice}>
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Invoice</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this invoice? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteInvoice} disabled={isDeleting}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteInvoice}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Invoice'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  );
};

export default InvoiceDetailsDialog;
