
import { jsPDF } from 'jspdf';
import { Invoice } from '@/types/damages';
import { supabase } from '@/integrations/supabase/client';

interface Property {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
  address?: string;
  notes?: string; // Address is stored in notes field
  city?: string;
  state?: string;
  zip?: string;
}

export const generateInvoicePdf = async (
  invoice: Invoice,
  damageReportId: string,
  property: Property,
  provider: Provider
): Promise<void> => {
  // Fetch invoice items
  const { data: itemsData, error: itemsError } = await supabase
    .from('invoice_items')
    .select('*')
    .eq('invoice_id', invoice.id)
    .order('created_at', { ascending: true });

  if (itemsError) {
    console.error('Error fetching invoice items:', itemsError);
  }

  const invoiceItems: InvoiceItem[] = itemsData || [];

  const doc = new jsPDF();

  // Set document properties
  doc.setProperties({
    title: `Invoice ${invoice.invoice_number}`,
    subject: 'Damage Report Invoice',
    author: 'Property Manager',
    creator: 'Property Management System'
  });

  // Page dimensions
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 20;

  // Draw border around the entire page
  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.5);
  doc.rect(10, 10, pageWidth - 20, pageHeight - 20);

  // Company/Provider Information (Top Left)
  let yPos = 25;
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(12);
  doc.text(provider.name || 'Company Name', margin, yPos);

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  yPos += 5;

  // Provider address (from notes field or address field)
  const providerAddress = provider.address || provider.notes;
  if (providerAddress) {
    const addressLines = providerAddress.split('\n');
    addressLines.forEach(line => {
      if (line.trim()) {
        doc.text(line.trim(), margin, yPos);
        yPos += 4;
      }
    });
  }

  // Provider contact info
  if (provider.phone) {
    doc.text(`Phone: ${provider.phone}`, margin, yPos);
    yPos += 4;
  }

  if (provider.email) {
    doc.text(`Email: ${provider.email}`, margin, yPos);
  }

  // INVOICE title (Top Right)
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(24);
  doc.setTextColor(150, 150, 150); // Gray color
  doc.text('INVOICE', pageWidth - margin, 30, { align: 'right' });

  // Invoice details table (Top Right)
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  // Draw invoice details box
  const boxX = pageWidth - 80;
  const boxY = 40;
  const boxWidth = 60;
  const boxHeight = 20;

  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.3);
  doc.rect(boxX, boxY, boxWidth, boxHeight);

  // Invoice details headers
  doc.setFillColor(220, 220, 220);
  doc.rect(boxX, boxY, boxWidth, 5, 'F');
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(8);
  doc.text('INVOICE #', boxX + 2, boxY + 3);
  doc.text('DATE', boxX + 32, boxY + 3);

  // Invoice details values
  doc.setFont('helvetica', 'normal');
  doc.text(invoice.invoice_number || 'N/A', boxX + 2, boxY + 8);
  doc.text(formatDate(invoice.issue_date || new Date().toISOString()), boxX + 32, boxY + 8);

  // BILL TO section
  yPos = 80;
  doc.setFillColor(220, 220, 220);
  doc.rect(margin, yPos, 100, 8, 'F');
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('BILL TO', margin + 2, yPos + 5);

  yPos += 12;
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  
  // Use real property data
  if (property.name) {
    doc.text(property.name, margin, yPos);
    yPos += 4;
    // Don't duplicate the property name
  }

  // Property address
  if (property.address) {
    doc.text(property.address, margin, yPos);
    yPos += 4;
  }

  const cityStateZip = [property.city, property.state].filter(Boolean).join(', ');
  if (cityStateZip) {
    doc.text(cityStateZip, margin, yPos);
    yPos += 4;
  }

  // Skip phone and email since we don't have this data for properties

  // Items table
  yPos = 160;

  // Table header
  doc.setFillColor(220, 220, 220);
  doc.rect(margin, yPos, pageWidth - 2 * margin, 8, 'F');
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('DESCRIPTION', margin + 2, yPos + 5);
  doc.text('AMOUNT', pageWidth - margin - 2, yPos + 5, { align: 'right' });

  yPos += 12;

  // Invoice items
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  if (invoiceItems.length > 0) {
    invoiceItems.forEach(item => {
      doc.text(item.description || 'No description', margin + 2, yPos);
      doc.text(item.amount?.toFixed(2) || '0.00', pageWidth - margin - 2, yPos, { align: 'right' });
      yPos += 6;
    });
  } else {
    // Fallback to sample items if no items found
    const sampleItems = [
      { description: 'Service Fee', amount: 200.00 },
      { description: 'Labor: 2 hours at $75/hr', amount: 150.00 },
      { description: 'New client discount', amount: -50.00 },
      { description: 'Tax (4.25% after discount)', amount: 12.75 }
    ];

    sampleItems.forEach(item => {
      doc.text(item.description, margin + 2, yPos);
      doc.text(item.amount.toFixed(2), pageWidth - margin - 2, yPos, { align: 'right' });
      yPos += 6;
    });
  }

  // Add space for more items
  yPos = Math.max(yPos, 220);

  // Thank you message
  doc.setFont('helvetica', 'italic');
  doc.setFontSize(9);
  doc.text('Thank you for your business!', margin + 2, yPos);

  // Total section
  const totalBoxY = yPos + 5;
  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.3);
  doc.rect(pageWidth - 80, totalBoxY, 60, 12);

  doc.setFillColor(220, 220, 220);
  doc.rect(pageWidth - 80, totalBoxY, 30, 12, 'F');

  doc.setFont('helvetica', 'bold');
  doc.setFontSize(10);
  doc.text('TOTAL', pageWidth - 75, totalBoxY + 7);
  doc.text('$', pageWidth - 45, totalBoxY + 7);
  doc.text(`${invoice.total_amount?.toFixed(2) || '312.75'}`, pageWidth - 25, totalBoxY + 7, { align: 'right' });

  // Footer message
  yPos = totalBoxY + 30;
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(8);
  doc.text('If you have any questions about this invoice, please contact', pageWidth / 2, yPos, { align: 'center' });
  yPos += 4;

  // Use actual provider contact info
  const contactInfo: string[] = [];
  if (provider.name) contactInfo.push(provider.name);
  if (provider.phone) contactInfo.push(provider.phone);
  if (provider.email) contactInfo.push(provider.email);

  const contactText = contactInfo.length > 0
    ? contactInfo.join(', ')
    : '[Name, Phone, <EMAIL>]';

  doc.text(contactText, pageWidth / 2, yPos, { align: 'center' });

  // Save the PDF
  doc.save(`invoice-${invoice.invoice_number || damageReportId}.pdf`);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};
