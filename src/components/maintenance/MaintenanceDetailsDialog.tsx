import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { MaintenanceTask, MaintenanceStatus, Provider } from './types';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import RecurringTaskSeriesComponent from './RecurringTaskSeries';
import { formatRecurrenceDescription } from '@/utils/recurringTasks';
import {
  CalendarDays,
  Home,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Clock,
  Edit,
  Mail,
  CircleSlash,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import AddMaintenanceDialog from './AddMaintenanceDialog';
import { useMaintenanceTaskForm } from './hooks/useMaintenanceTaskForm';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import Tooltip from '@/components/ui/Tooltip';
import { syncPropertyCalendarIfNeeded } from '@/utils/calendarUtils';

interface MaintenanceDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: MaintenanceTask;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onTaskUpdated?: () => void;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
  providers?: Provider[];
}

const MaintenanceDetailsDialog: React.FC<MaintenanceDetailsDialogProps> = ({
  open,
  onOpenChange,
  task,
  onStatusChange,
  onTaskUpdated,
  onDeleteTask,
  providers = []
}) => {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const { authState } = useAuth();
  const userId = authState.user?.id;

  // Sync property calendar when dialog opens
  useEffect(() => {
    if (open && task && task.propertyId && userId) {
      // Fetch the property details first
      const fetchPropertyAndSync = async () => {
        try {
          const { data: property, error } = await supabase
            .from('properties')
            .select('*')
            .eq('id', task.propertyId)
            .single();

          if (error) {
            console.error('[MaintenanceDetailsDialog] Error fetching property:', error);
            return;
          }

          if (property) {
            console.log('[MaintenanceDetailsDialog] Syncing calendar for property:', property.name);
            await syncPropertyCalendarIfNeeded(property, userId);
          }
        } catch (error) {
          console.error('[MaintenanceDetailsDialog] Error syncing property calendar:', error);
        }
      };

      fetchPropertyAndSync();
    }
  }, [open, task, userId]);

  const renderProviderEmailButton = () => {
    // Always show the button if there's a provider email, even if providerId is missing
    if (!task.providerEmail) {
      return null;
    }

    console.log('Provider email found:', task.providerEmail);

    return (
      <Tooltip text="Resend email notification to the service provider" position="top">
        <Button
          variant="outline"
          size="sm"
          className="ml-2"
          onClick={handleResendEmail}
          disabled={isResendingEmail}
        >
          <Mail className="h-4 w-4 mr-1" />
          Resend Email
        </Button>
      </Tooltip>
    );
  };

  const handleStatusChange = async (newStatus: MaintenanceStatus) => {
    if (!onStatusChange) return;

    try {
      const result = await onStatusChange(task.id, newStatus);
      if (result) {
        toast.success(`Task marked as ${newStatus}`);
        if (onTaskUpdated) {
          onTaskUpdated();
        }
      } else {
        toast.error('Failed to update task status');
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
    }
  };

  const handleDeleteTask = async () => {
    if (!onDeleteTask) return;

    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        const result = await onDeleteTask(task.id);
        if (result) {
          toast.success('Task deleted successfully');
          onOpenChange(false);
        } else {
          toast.error('Failed to delete task');
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        toast.error('Failed to delete task');
      }
    }
  };

  const getStatusBadgeColor = (status: MaintenanceStatus) => {
    switch (status) {
      case 'new': return 'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700';
      case 'assigned': return 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700';
      case 'in_progress': return 'bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700';
      case 'completed': return 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700';
      case 'cancelled': return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700';
      case 'accepted': return 'bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700';
      case 'rejected': return 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700';
      default: return 'bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700';
    }
  };

  const getSeverityColor = (severity: MaintenanceTask['severity']) => {
    switch (severity) {
      case 'low': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-amber-600 dark:text-amber-400';
      case 'high': return 'text-orange-600 dark:text-orange-400';
      case 'critical': return 'text-red-600 dark:text-red-400';
      default: return 'text-muted-foreground';
    }
  };

  // Generates a token for the accept/reject links
  const generateResponseToken = async (taskId: string): Promise<string | null> => {
    // Check for secure context (HTTPS or localhost) required by crypto.subtle
    if (!crypto?.subtle?.digest) {
      console.error("Web Crypto API (crypto.subtle.digest) not available. Requires a secure context (HTTPS or localhost).");
      toast.error("Cannot generate secure link: Web Crypto API not available. Ensure you are using HTTPS or localhost.");
      return null; // Indicate failure
    }

    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(taskId + "maintenance-response-secret"); // Use a secret known by the backend
      const hashBuffer = await crypto.subtle.digest("SHA-256", data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      // Convert hash to hex string and take first 32 chars
      return hashArray.map(b => b.toString(16).padStart(2, "0")).join("").substring(0, 32);
    } catch (error) {
      console.error("Error generating response token:", error);
      toast.error("Failed to generate secure response token.");
      return null; // Indicate failure
    }
  };

  const handleResendEmail = async () => {
    if (!task.providerEmail) {
      toast.error("No provider email available");
      return;
    }

    console.log('Resending email to provider:', task.providerEmail);

    setIsResendingEmail(true);
    try {
      const provider = providers.find(p => p.id === task.providerId);
      const userEmail = authState.user?.email;

      // Generate the token, checking for errors (like missing secure context)
      const responseToken = await generateResponseToken(task.id);
      if (!responseToken) {
        // Error already shown by generateResponseToken
        setIsResendingEmail(false);
        return;
      }

      const baseAppUrl = window.location.origin;
      const acceptUrl = `${baseAppUrl}/#/maintenance/response?taskId=${task.id}&action=accept&token=${responseToken}`;
      // Ensure 'decline' action is used here to match backend
      const rejectUrl = `${baseAppUrl}/#/maintenance/response?taskId=${task.id}&action=decline&token=${responseToken}`;

      console.log('Resending provider email with details:', {
        taskId: task.id,
        providerId: task.providerId,
        providerEmail: task.providerEmail,
        acceptUrl,
        rejectUrl
      });

      const { error: invokeError } = await supabase.functions.invoke('send-email', {
        body: {
          from: `Property Manager <<EMAIL>>`,
          to: task.providerEmail,
          subject: `Maintenance Task: ${task.title}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>Maintenance Task: ${task.title}</h2>
              <p><strong>Property:</strong> ${task.propertyName || 'Not specified'}</p>
              <p><strong>Severity:</strong> ${task.severity}</p>
              <p><strong>Due Date:</strong> ${task.dueDate || 'No due date'}</p>
              <p><strong>Description:</strong> ${task.description}</p>
              <p>Please contact ${userEmail || 'the property manager'} for more information.</p>

              <div style="margin: 30px 0;">
                <p><strong>Are you available to handle this task?</strong></p>
                <table cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td style="padding-right: 15px;">
                      <a href="${acceptUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                        Accept Task
                      </a>
                    </td>
                    <td>
                      <a href="${rejectUrl}" style="display: inline-block; background-color: #e53935; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                        Decline Task
                      </a>
                    </td>
                  </tr>
                </table>
              </div>

              <p style="font-size: 0.8em; color: #666;">
                By accepting, you agree to take responsibility for this maintenance task.
                If you decline, the property manager will be notified to find another service provider.
              </p>
            </div>
          `,
          reply_to: userEmail
        }
      });

      if (invokeError) throw invokeError; // Throw the specific error from invoke

      toast.success('Email resent to provider successfully');
    } catch (err) { // Use a different variable name like 'err'
      console.error('Error sending provider notification:', err);
      toast.error('Failed to resend email to provider');
    } finally {
      setIsResendingEmail(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-xl">{task.title}</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="details" className="mt-4">
            <TabsList className={`grid w-full ${task.isRecurring ? 'grid-cols-3' : 'grid-cols-2'}`}>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              {task.isRecurring && <TabsTrigger value="recurring">Recurring</TabsTrigger>}
            </TabsList>

            <TabsContent value="details" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Status</p>
                  <Badge className={cn("font-normal text-white", getStatusBadgeColor(task.status as MaintenanceStatus))}>
                    {task.status}
                  </Badge>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Severity</p>
                  <div className="flex items-center">
                    <AlertTriangle className={cn("h-4 w-4 mr-1", getSeverityColor(task.severity))} />
                    <span className="capitalize text-primary">{task.severity}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Property</p>
                  <div className="flex items-center">
                    <Home className="h-4 w-4 mr-1 text-muted" />
                    <span className="text-primary">{task.propertyName || 'Not specified'}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Due Date</p>
                  <div className="flex items-center">
                    <CalendarDays className="h-4 w-4 mr-1 text-muted" />
                    <span className="text-primary">{task.dueDate || 'No due date'}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Assigned To</p>
                  <span className="text-primary">{task.assignedTo || 'Not assigned'}</span>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Service Provider</p>
                  <span className="text-primary">{providers.find(p => p.id === task.providerId)?.name || 'Not assigned'}</span>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium mb-1 text-primary">Description</p>
                <div className="surface-secondary p-3 rounded-md">
                  <p className="whitespace-pre-line text-primary">{task.description}</p>
                </div>
              </div>

              {/* Recurring Task Information */}
              {task.isRecurring && (
                <div>
                  <p className="text-sm font-medium mb-1 text-primary">Recurring Schedule</p>
                  <div className="status-info p-3 rounded-md border">
                    <p>
                      {task.recurrenceIntervalDays && formatRecurrenceDescription(
                        task.recurrenceIntervalDays,
                        task.maxRecurrences
                      )}
                    </p>
                    {task.recurrenceCount !== undefined && task.recurrenceCount > 0 && (
                      <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                        This is occurrence #{task.recurrenceCount + 1} in the series
                      </p>
                    )}
                  </div>
                </div>
              )}

              <Separator />

              <div className="flex flex-wrap gap-2">
                {/* Always show Complete and In Progress buttons for all users */}
                <Tooltip text="Mark task as completed" position="top">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('completed')}
                    className="flex items-center text-primary"
                    disabled={task.status === 'completed'}
                  >
                    <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                    Complete
                  </Button>
                </Tooltip>

                <Tooltip text="Mark task as in progress" position="top">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStatusChange('in_progress')}
                    className="flex items-center text-primary"
                    disabled={task.status === 'in_progress'}
                  >
                    <Clock className="h-4 w-4 mr-1 text-purple-500" />
                    In Progress
                  </Button>
                </Tooltip>

                {/* Show Cancel, Edit, and Delete buttons for task creators and property managers */}
                {(authState?.profile?.role !== 'service_provider' || task.userId === userId) && (
                  <>
                    <Tooltip text="Cancel this task" position="top">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange('cancelled')}
                        className="flex items-center"
                        disabled={task.status === 'cancelled'}
                      >
                        <CircleSlash className="h-4 w-4 mr-1 text-gray-500" />
                        Cancel
                      </Button>
                    </Tooltip>

                    <Tooltip text="Edit task details" position="top">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditDialogOpen(true)}
                        className="flex items-center"
                      >
                        <Edit className="h-4 w-4 mr-1 text-blue-500" />
                        Edit
                      </Button>
                    </Tooltip>

                    {renderProviderEmailButton()}

                    <div className="ml-auto">
                      <Tooltip text="Delete this task" position="top">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleDeleteTask}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </Tooltip>
                    </div>
                  </>
                )}


              </div>
            </TabsContent>

            <TabsContent value="history" className="pt-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                  <p className="text-sm text-primary">Created {task.createdAt && formatDistanceToNow(new Date(task.createdAt), { addSuffix: true })}</p>
                </div>

                {task.createdAt && (
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                    <p className="text-sm text-primary">Last activity {formatDistanceToNow(new Date(task.createdAt), { addSuffix: true })}</p>
                  </div>
                )}

                <div className="text-center text-muted-foreground text-sm mt-8">
                  More detailed task history will be available in a future update.
                </div>
              </div>
            </TabsContent>

            {/* Recurring Task Series Tab */}
            {task.isRecurring && (
              <TabsContent value="recurring" className="pt-4">
                <RecurringTaskSeriesComponent
                  taskId={task.id}
                  isRecurring={task.isRecurring}
                  onTaskSelect={(taskId) => {
                    // Could implement navigation to other tasks in the series
                    console.log('Selected task:', taskId);
                  }}
                />
              </TabsContent>
            )}
          </Tabs>
        </DialogContent>
      </Dialog>

      {isEditDialogOpen && (
        <AddMaintenanceDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onTaskCreated={() => {
            if (onTaskUpdated) {
              onTaskUpdated();
            }
          }}
          providers={providers}
          initialTask={task}
        />
      )}
    </>
  );
};

export default MaintenanceDetailsDialog;
