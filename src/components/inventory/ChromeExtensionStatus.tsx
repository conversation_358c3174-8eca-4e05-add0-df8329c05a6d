import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle2, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { detectStayFuExtension, getCachedExtensionInfo, setCachedExtensionInfo, type ExtensionInfo } from '@/utils/extensionDetection';

const ChromeExtensionStatus = () => {
  const [status, setStatus] = useState<'checking' | 'not_installed' | 'not_connected' | 'connected'>('checking');
  const [extensionInfo, setExtensionInfo] = useState<ExtensionInfo | null>(null);

  useEffect(() => {
    checkExtensionStatus();
  }, []);

  const checkExtensionStatus = async () => {
    setStatus('checking');

    try {
      // Try to get cached info first
      let info = getCachedExtensionInfo();
      
      // If no cached info or cached info shows not installed, try detection
      if (!info || !info.isInstalled) {
        console.log('ChromeExtensionStatus: Detecting StayFu extension...');
        info = await detectStayFuExtension();
        setCachedExtensionInfo(info);
      }

      setExtensionInfo(info);

      if (!info) {
        console.log('ChromeExtensionStatus: No StayFu extension found');
        setStatus('not_installed');
        return;
      }

      if (!info.isInstalled) {
        console.log('ChromeExtensionStatus: Extension not installed');
        setStatus('not_installed');
        return;
      }

      if (!info.isConnected) {
        console.log('ChromeExtensionStatus: Extension installed but not connected');
        setStatus('not_connected');
        return;
      }

      console.log(`ChromeExtensionStatus: Extension connected with ID: ${info.id}`);
      setStatus('connected');
    } catch (error) {
      console.error('ChromeExtensionStatus: Error checking extension status:', error);
      setStatus('not_installed');
    }
  };

   // --- Helper function to open settings ---
   const openExtensionSettings = async () => {
     try {
       // Use the detected extension info
       if (extensionInfo?.isInstalled && typeof chrome !== "undefined" && chrome.runtime) {
          const settingsUrl = `chrome-extension://${extensionInfo.id}/settings.html`;
          window.open(settingsUrl, '_blank');
       } else {
          toast.error("Cannot open settings: Extension not detected or Chrome API not available.");
       }
     } catch (e) {
       console.error("Could not open settings page", e);
       toast.error("Failed to open extension settings.");
     }
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'checking':
        return (
          <Alert>
            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
            <AlertTitle>Checking Extension Status</AlertTitle>
            <AlertDescription>
              Detecting StayFu Chrome Extension...
            </AlertDescription>
          </Alert>
        );

      case 'not_installed':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Extension Not Found</AlertTitle>
            <AlertDescription className="space-y-2">
               <p>
                 The StayFu Chrome Extension was not detected. Please ensure it's installed and enabled in your browser.
               </p>
               <p className="text-xs text-muted-foreground">
                 You may need to refresh this page after installing or enabling the extension.
                 {extensionInfo?.id && ` (Tried extension ID: ${extensionInfo.id})`}
               </p>
            </AlertDescription>
          </Alert>
        );

      case 'not_connected':
        return (
          <Alert variant="default"> {/* Using default variant for warning */}
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Extension Not Connected</AlertTitle>
            <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
               <span>
                 The extension is installed but not connected to your StayFu account.
                 {extensionInfo?.id && ` (Extension ID: ${extensionInfo.id})`}
               </span>
               <Button
                 variant="outline"
                 size="sm"
                 onClick={openExtensionSettings}
                 className="mt-2 sm:mt-0 sm:ml-4 flex-shrink-0" // Added flex-shrink-0
               >
                 <Settings className="mr-2 h-4 w-4" />
                 Open Extension Settings
               </Button>
             </AlertDescription>
          </Alert>
        );

      case 'connected':
        return (
          <Alert className="bg-green-50 border-green-600">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Extension Connected</AlertTitle>
            <AlertDescription className="text-green-700">
              Ready to search and import Amazon products.
              {extensionInfo?.id && (
                <div className="text-xs mt-1 text-green-600">Extension ID: {extensionInfo.id}</div>
              )}
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <div className="mb-6">
      {getStatusDisplay()}
    </div>
  );
};

export default ChromeExtensionStatus;
