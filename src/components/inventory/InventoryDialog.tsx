import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import InventoryImageUploader from './InventoryImageUploader';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FormattedInventoryItem } from '@/types/inventory';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface InventoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (item: FormattedInventoryItem) => void;
  item: Partial<FormattedInventoryItem> | null;
  properties: { id: string; name: string }[];
}

const InventoryDialog: React.FC<InventoryDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  item,
  properties
}) => {
  const [formData, setFormData] = useState<Partial<FormattedInventoryItem>>({
    name: '',
    propertyId: '',
    collection: '',
    quantity: 0,
    minQuantity: 1,
    price: undefined,
    amazonUrl: '',
    walmartUrl: '',
    imageUrl: '',
    hasProcessedImage: false
  });



  const [collections, setCollections] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('general');
  const [isLoading, setIsLoading] = useState(false);

  // Reset form data when dialog opens with a new item
  useEffect(() => {
    if (isOpen && item) {
      console.log('[InventoryDialog] Opening dialog with item:', item);
      console.log('[InventoryDialog] Item collection value:', item.collection);
      console.log('[InventoryDialog] Type of item.collection:', typeof item.collection);

      // Copy all fields from item to formData
      const newFormData = {
        ...item,
        // Ensure these fields are always present even if they're undefined in the item
        imageUrl: item.imageUrl || '',
        propertyId: item.propertyId || '__none__',
        collection: item.collection || '__none__',
        quantity: typeof item.quantity === 'number' ? item.quantity : 0,
        minQuantity: typeof item.minQuantity === 'number' ? item.minQuantity : 1,
        price: item.price,
        amazonUrl: item.amazonUrl || '',
        walmartUrl: item.walmartUrl || '',
        hasProcessedImage: item.hasProcessedImage || false,
        // Ensure any fields that were undefined in the item are set to empty strings
      };

      console.log('[InventoryDialog] Formatted form data:', newFormData);
      console.log('[InventoryDialog] Form collection value:', newFormData.collection);

      setFormData(newFormData);



      // Set the active tab to 'general'
      setActiveTab('general');

      // Load collections for this property if a property is selected
      if (item.propertyId) {
        loadCollectionsForProperty(item.propertyId);
      }
    }
  }, [isOpen, item]);

  // Updated collections loading function
  const loadCollectionsForProperty = async (propertyId: string) => {
    try {
      setIsLoading(true);

      // Import supabase client
      const { supabase } = await import('@/integrations/supabase/client');

      // Fetch collections for the selected property directly from the properties table
      const { data, error } = await supabase
        .from('properties')
        .select('collections')
        .eq('id', propertyId)
        .single();

      if (error) {
        console.error('Error fetching property collections:', error);
        setCollections([]);
        return;
      }

      if (data?.collections && Array.isArray(data.collections)) {
        // Process collections - they can be strings or objects with name property
        const collectionNames = data.collections
          .map((col: any) => {
            if (typeof col === 'string' && col.trim()) {
              return col.trim();
            } else if (col && typeof col === 'object' && col.name && typeof col.name === 'string') {
              return col.name.trim();
            }
            return null;
          })
          .filter((name: string | null): name is string => name !== null && name !== '');

        console.log('[InventoryDialog] Loaded collections for property:', propertyId, collectionNames);
        console.log('[InventoryDialog] Setting collections state to:', collectionNames);
        setCollections(collectionNames);
      } else {
        console.log('[InventoryDialog] No collections found for property:', propertyId);
        setCollections([]);
      }
    } catch (error) {
      console.error('Error loading collections:', error);
      setCollections([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === 'quantity' || name === 'minQuantity' || name === 'price') {
      // Convert to number and validate
      const numValue = parseFloat(value);
      setFormData({
        ...formData,
        [name]: isNaN(numValue) ? 0 : numValue
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle property change
  const handlePropertyChange = (value: string) => {
    console.log('[InventoryDialog] Property changed to:', value);
    // Ensure we never pass empty strings
    const safeValue = value || '__none__';
    setFormData({
      ...formData,
      propertyId: safeValue,
      // Also clear the collection when property changes
      collection: '__none__'
    });

    // Load collections for the selected property
    if (value && value !== '__none__') {
      loadCollectionsForProperty(value);
    } else {
      setCollections([]);
    }
  };

  // Handle collection change
  const handleCollectionChange = (value: string) => {
    console.log('[InventoryDialog] Collection changed to:', value);
    // Ensure we never pass empty strings
    const safeValue = value || '__none__';
    setFormData({
      ...formData,
      collection: safeValue
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.propertyId || formData.propertyId === '__none__') {
      alert('Please fill out all required fields.');
      return;
    }

    // Prepare the item for saving, including the original ID if we're editing
    const itemToSave: FormattedInventoryItem = {
      id: item?.id || '', // Keep the ID if we're editing
      name: formData.name || '',
      propertyId: formData.propertyId || '',
      propertyName: properties.find(p => p.id === formData.propertyId)?.name || '',
      collection: formData.collection === '__none__' ? '' : (formData.collection || ''),
      quantity: typeof formData.quantity === 'number' ? formData.quantity : 0,
      minQuantity: typeof formData.minQuantity === 'number' ? formData.minQuantity : 1,
      price: formData.price,
      amazonUrl: formData.amazonUrl || undefined,
      walmartUrl: formData.walmartUrl || undefined,
      imageUrl: formData.imageUrl || undefined,
      hasProcessedImage: formData.hasProcessedImage || false,
      teamId: item?.teamId // Preserve the team ID if it exists
    };

    console.log('[InventoryDialog] Saving item:', itemToSave);
    console.log('[InventoryDialog] Collection being saved:', itemToSave.collection);
    console.log('[InventoryDialog] Form collection value was:', formData.collection);

    // Call the onSave callback with the item
    onSave(itemToSave);
  };





  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {item?.id ? 'Edit inventory item' : 'Add inventory item'}
            </DialogTitle>
            <DialogDescription>
              {item?.id
                ? 'Update the details of this inventory item.'
                : 'Add a new item to your inventory. Fill in the required information below.'
              }
            </DialogDescription>
          </DialogHeader>

          {properties.length === 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    No Properties Available
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      You need to create at least one property before adding inventory items.
                      Properties help organize your inventory by location or category.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="links">Links</TabsTrigger>
                <TabsTrigger value="image">Image</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4 pt-4">
                {/* General tab content */}
                <div className="space-y-2">
                  <Label htmlFor="name">Name*</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Item name"
                    value={formData.name || ''}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property">Property*</Label>
                  <Select
                    value={formData.propertyId || '__none__'}
                    onValueChange={handlePropertyChange}
                    required
                  >
                    <SelectTrigger id="property">
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.length > 0 ? (
                        properties.map(property => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="__no_properties__" disabled>
                          No properties available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="collection">Collection</Label>
                  <Select
                    value={formData.collection || '__none__'}
                    onValueChange={handleCollectionChange}
                    disabled={!formData.propertyId || isLoading}
                  >
                    <SelectTrigger id="collection">
                      <SelectValue placeholder="Select collection" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="__none__">None</SelectItem>
                      {collections.length > 0 ? (
                        collections.map((collection, index) => (
                          <SelectItem key={index} value={collection}>{collection}</SelectItem>
                        ))
                      ) : (
                        <SelectItem value="__no_collections__" disabled>
                          {formData.propertyId ? 'No collections available for this property' : 'Select a property first'}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Current Quantity</Label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min={0}
                      value={formData.quantity}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="minQuantity">Minimum Quantity</Label>
                    <Input
                      id="minQuantity"
                      name="minQuantity"
                      type="number"
                      min={0}
                      value={formData.minQuantity}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">Price</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    min={0}
                    step="0.01"
                    value={formData.price || ''}
                    onChange={handleInputChange}
                    placeholder="0.00"
                  />
                </div>
              </TabsContent>

              <TabsContent value="links" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amazonUrl">Amazon URL</Label>
                    <Input
                      id="amazonUrl"
                      name="amazonUrl"
                      type="url"
                      value={formData.amazonUrl || ''}
                      onChange={handleInputChange}
                      placeholder="Enter Amazon URL"
                    />
                  </div>

                  <div>
                    <Label htmlFor="walmartUrl">Walmart URL</Label>
                    <Input
                      id="walmartUrl"
                      name="walmartUrl"
                      type="url"
                      value={formData.walmartUrl || ''}
                      onChange={handleInputChange}
                      placeholder="Enter Walmart URL"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="image" className="space-y-4">
                <div className="space-y-4">
                  <InventoryImageUploader
                    imageUrl={formData.imageUrl || ''}
                    onImageChange={(newUrl) => {
                      setFormData({
                        ...formData,
                        imageUrl: newUrl,
                        hasProcessedImage: false
                      });
                    }}
                  />

                  <div>
                    <Label htmlFor="imageUrl">Or enter image URL directly</Label>
                    <Input
                      id="imageUrl"
                      type="url"
                      value={formData.imageUrl}
                      onChange={(e) => {
                        const newUrl = e.target.value;
                        setFormData({
                          ...formData,
                          imageUrl: newUrl,
                          hasProcessedImage: false
                        });
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} className="mr-2">
                Cancel
              </Button>
              <Button type="submit">
                {item?.id ? 'Update Item' : 'Add Item'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>


    </>
  );
};

export default InventoryDialog;