import React, { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from '@/integrations/supabase/client';
import { Search, AlertCircle, Filter, SlidersHorizontal } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AmazonProduct } from './types';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
// import AmazonProductCard from './AmazonProductCard';
// import { useInventoryOperations } from '@/hooks/useInventoryOperations';
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import ExtensionAutoConfig from './ExtensionAutoConfig';
import { ExtensionImportProduct } from './types';
import { detectStayFuExtension, sendMessageToExtension, getCachedExtensionInfo, setCachedExtensionInfo, type ExtensionInfo } from '@/utils/extensionDetection';
import { decodeHtmlEntities } from '@/utils/formatters';
// import ChromeExtensionStatus from './ChromeExtensionStatus';

const AmazonSearch = () => {
  // Use team-aware properties hook instead of direct query
  const { properties: teamProperties = [] } = usePropertiesQueryV2();
  
  // Type assertion to ensure proper typing
  const typedProperties = teamProperties as Array<{ id: string; name: string; collections?: any[] }>;
  
  const [searchQuery, setSearchQuery] = useState('');
  const [collections, setCollections] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<AmazonProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [isExtensionInstalled, setIsExtensionInstalled] = useState(false);
  const [isExtensionConnected, setIsExtensionConnected] = useState(false);
  const [isCheckingExtension, setIsCheckingExtension] = useState(true);
  const [importStep, setImportStep] = useState<'search' | 'results' | 'import'>('search');
  const [selectedImportProperties, setSelectedImportProperties] = useState<{[asin: string]: string}>({});
  const [selectedImportCollections, setSelectedImportCollections] = useState<{[asin: string]: string}>({});
  const [selectedImportQuantities, setSelectedImportQuantities] = useState<{[asin: string]: number}>({});
  const [selectedImportMinQuantities, setSelectedImportMinQuantities] = useState<{[asin: string]: number}>({});
  const [propertyCollectionsMap, setPropertyCollectionsMap] = useState<{[propertyId: string]: string[]}>({});
  const [extensionInfo, setExtensionInfo] = useState<ExtensionInfo | null>(null);

  // Filter and sort state
  const [showFilters, setShowFilters] = useState(false);
  const [priceFilter, setPriceFilter] = useState({ min: '', max: '' });
  const [searchTermFilter, setSearchTermFilter] = useState('');
  const [sortBy, setSortBy] = useState<'relevance' | 'price-low' | 'price-high' | 'rating' | 'reviews'>('relevance');
  const [primeOnly, setPrimeOnly] = useState(false);
  const [minRating, setMinRating] = useState<number>(0); // 0 means no filter

  // Load saved search results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('amazon_search_results');
    const savedQuery = localStorage.getItem('amazon_search_query');
    const savedStep = localStorage.getItem('amazon_import_step');
    
    if (savedResults) {
      try {
        const results = JSON.parse(savedResults);
        if (Array.isArray(results) && results.length > 0) {
          setSearchResults(results);
          setImportStep((savedStep as any) || 'results');
          console.log(`Restored ${results.length} search results from localStorage`);
        }
      } catch (error) {
        console.error('Error parsing saved search results:', error);
        localStorage.removeItem('amazon_search_results');
      }
    }
    
    if (savedQuery) {
      setSearchQuery(savedQuery);
    }
  }, []);

  // Filtered and sorted search results
  const filteredAndSortedResults = useMemo(() => {
    let filtered = [...searchResults];

    // Apply search term filter
    if (searchTermFilter.trim()) {
      const searchTerm = searchTermFilter.toLowerCase();
      filtered = filtered.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.asin.toLowerCase().includes(searchTerm)
      );
    }

    // Apply price filter
    if (priceFilter.min || priceFilter.max) {
      filtered = filtered.filter(product => {
        const price = product.numericPrice || 0;
        const min = priceFilter.min ? parseFloat(priceFilter.min) : 0;
        const max = priceFilter.max ? parseFloat(priceFilter.max) : Infinity;
        return price >= min && price <= max;
      });
    }

    // Apply Prime filter
    if (primeOnly) {
      filtered = filtered.filter(product => product.isPrime);
    }

    // Apply minimum rating filter
    if (minRating > 0) {
      filtered = filtered.filter(product => (product.rating || 0) >= minRating);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return (a.numericPrice || 0) - (b.numericPrice || 0);
        case 'price-high':
          return (b.numericPrice || 0) - (a.numericPrice || 0);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'reviews':
          return (b.reviewCount || 0) - (a.reviewCount || 0);
        case 'relevance':
        default:
          return 0; // Keep original order for relevance
      }
    });

    return filtered;
  }, [searchResults, searchTermFilter, priceFilter, primeOnly, minRating, sortBy]);

  // Save search results to localStorage whenever they change
  useEffect(() => {
    if (searchResults.length > 0) {
      localStorage.setItem('amazon_search_results', JSON.stringify(searchResults));
      localStorage.setItem('amazon_import_step', importStep);
    } else {
      localStorage.removeItem('amazon_search_results');
      localStorage.removeItem('amazon_import_step');
    }
  }, [searchResults, importStep]);

  // Save search query to localStorage
  useEffect(() => {
    if (searchQuery) {
      localStorage.setItem('amazon_search_query', searchQuery);
    }
  }, [searchQuery]);

  // Deduplicate properties to prevent duplicates in the dropdown
  const properties = useMemo(() => {
    // Create a map to store unique properties by name
    const uniquePropertiesByName = new Map<string, { id: string; name: string }>();

    // Process properties to keep only one entry per property name
    // We'll clean the property name by removing user info in parentheses
    for (const property of typedProperties) {
      // Extract the base property name without the user info in parentheses
      const baseName = property.name.replace(/\s*\([^)]*\)\s*$/, '').trim();
      const key = baseName.toLowerCase();

      if (!uniquePropertiesByName.has(key)) {
        // Store with the original name but keyed by the base name
        uniquePropertiesByName.set(key, {
          id: property.id,
          name: baseName // Use the cleaned name without user info
        });
      }
    }

    // Convert the map values to an array
    const uniqueProperties = Array.from(uniquePropertiesByName.values());

    console.log(`[AmazonSearch] Deduplicated properties: ${uniqueProperties.length} (from ${typedProperties.length})`);
    return uniqueProperties;
  }, [typedProperties]);

  // Fetch collections from team properties
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        // Get collections from the team properties we have access to
        if (typedProperties.length === 0) {
          setCollections([]);
          return;
        }

        const propertyIds = typedProperties.map(p => p.id);
        
        // Get collections from property definitions for team properties only
        const { data: propertiesData, error: propsError } = await supabase
          .from('properties')
          .select('collections')
          .in('id', propertyIds)
          .not('collections', 'is', null);

        if (propsError) {
          console.error('Error fetching property collections:', propsError);
          return;
        }

        // Extract unique collections from all properties
        const uniqueCollections = new Set<string>();
        
        propertiesData?.forEach((property: { collections: any[] }) => {
          if (property.collections && Array.isArray(property.collections)) {
            property.collections.forEach((col: any) => {
              const collectionName = typeof col === 'object' ? col.name : col;
              if (collectionName && typeof collectionName === 'string') {
                uniqueCollections.add(collectionName);
              }
            });
          }
        });

        // Also include collections from existing inventory items as fallback
        const { data: inventoryData, error: invError } = await supabase
          .from('inventory_items')
          .select('collection')
          .not('collection', 'is', null)
          .order('collection', { ascending: true });

        if (!invError && inventoryData) {
          inventoryData.forEach((item: { collection: string }) => {
            if (item.collection) uniqueCollections.add(item.collection);
          });
        }

        // If no collections found, provide defaults
        if (uniqueCollections.size === 0) {
          ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'].forEach(col => 
            uniqueCollections.add(col)
          );
        }

        setCollections(Array.from(uniqueCollections).sort());
      } catch (error) {
        console.error('Error in fetchCollections:', error);
      }
    };

    fetchCollections();
  }, [typedProperties]);

  // Function to fetch collections for a specific property
  const fetchCollectionsForProperty = async (propertyId: string): Promise<string[]> => {
    if (propertyCollectionsMap[propertyId]) {
      return propertyCollectionsMap[propertyId];
    }

    try {
      console.log('[AmazonSearch] Fetching collections for property:', propertyId);
      
      const { data, error } = await supabase
        .from('properties')
        .select('collections, name')
        .eq('id', propertyId)
        .single();
      
      if (error) {
        console.error('Error fetching property collections:', error);
        return ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'];
      }

      let propertyCollections: string[] = [];
      if (data?.collections?.length > 0) {
        console.log('[AmazonSearch] Found collections for property:', data.name, data.collections);
        // Extract collection names from the property's collections array
        propertyCollections = data.collections.map((col: any) => 
          typeof col === 'object' ? col.name : col
        ).filter(Boolean);
      } else {
        console.log('[AmazonSearch] No collections found for property, using defaults');
        // If no collections are set for this property, provide default options
        propertyCollections = ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'];
      }

      // Cache the result
      setPropertyCollectionsMap(prev => ({
        ...prev,
        [propertyId]: propertyCollections
      }));

      return propertyCollections;
    } catch (error) {
      console.error('Error in fetchCollectionsForProperty:', error);
      return ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'];
    }
  };

  // Check for extension on component mount
  useEffect(() => {
    checkExtension();

    // Listen for search results from the extension
    const handleSearchResults = (event: MessageEvent) => {
      if (event.data.type === 'STAYFU_SEARCH_RESULTS') {
        console.log('Received search results from extension:', event.data);
        handleSearchResultsReceived(event.data.searchTerm, event.data.products);
      }
    };

    window.addEventListener('message', handleSearchResults);

    return () => {
      window.removeEventListener('message', handleSearchResults);
    };
  }, []);

  // Handle search results received from extension
  const handleSearchResultsReceived = (searchTerm: string, products: any[]) => {
    if (!products || products.length === 0) {
      console.log(`AmazonSearch: No products found for "${searchTerm}"`);
      toast.warning(`No products found for "${searchTerm}"`);
      return;
    }

    console.log(`AmazonSearch: Received ${products.length} products for "${searchTerm}"`);

    // Format products for display
    const formattedProducts: AmazonProduct[] = products.map(product => ({
      asin: product.asin,
      title: decodeHtmlEntities(product.title || ''),
      price: product.price,
      numericPrice: product.numericPrice,
      rating: product.rating,
      reviewCount: product.reviewCount,
      img: product.img,
      isPrime: product.isPrime,
      searchTerm: product.searchTerm || searchTerm, // Use product.searchTerm if available
      url: product.url
    }));

    // Accumulate results instead of overwriting, but deduplicate by ASIN
    setSearchResults(prevResults => {
      // Create a map to store unique products by ASIN
      const productMap = new Map();
      
      // First add all existing products
      prevResults.forEach(product => {
        productMap.set(product.asin, product);
      });
      
      // Then add new products (will overwrite if same ASIN)
      formattedProducts.forEach(product => {
        productMap.set(product.asin, product);
      });
      
      const updatedResults = Array.from(productMap.values());
      console.log(`AmazonSearch: Total unique products: ${updatedResults.length} (was ${prevResults.length}, added ${formattedProducts.length})`);
      return updatedResults;
    });
    
    setImportStep('results');
    toast.success(`Found ${formattedProducts.length} products for "${searchTerm}" (Total: ${searchResults.length + formattedProducts.length})`);
    
    // Only set loading to false if this might be the final result
    // For multi-search, we'll rely on timeout to stop loading
    setIsLoading(false);
  };

  // Check if the extension is installed and connected
  const checkExtension = async () => {
    setIsCheckingExtension(true);

    try {
      // Try to get cached info first
      let info = getCachedExtensionInfo();
      
      // If no cached info or cached info shows not installed, try detection
      if (!info || !info.isInstalled) {
        console.log('AmazonSearch: Detecting StayFu extension...');
        info = await detectStayFuExtension();
        setCachedExtensionInfo(info);
      }

      setExtensionInfo(info);

      if (!info) {
        console.log('AmazonSearch: No StayFu extension found');
        setIsExtensionInstalled(false);
        setIsExtensionConnected(false);
      } else {
        setIsExtensionInstalled(info.isInstalled);
        setIsExtensionConnected(info.isConnected);
        
        if (info.isInstalled && info.isConnected) {
          console.log(`AmazonSearch: Extension ready with ID: ${info.id}`);
        } else if (info.isInstalled) {
          console.log(`AmazonSearch: Extension installed but not connected (ID: ${info.id})`);
        }
      }
    } catch (error) {
      console.error('AmazonSearch: Error checking extension:', error);
      setIsExtensionInstalled(false);
      setIsExtensionConnected(false);
    }

    setIsCheckingExtension(false);
  };

  // Handle search form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    if (!isExtensionInstalled || !extensionInfo) {
      toast.error('StayFu Chrome extension is not installed or detected.');
      return;
    }

    if (!isExtensionConnected) {
      toast.error('StayFu Chrome extension is not connected to your account.');
      return;
    }

    // Parse multiple search terms separated by commas
    const searchTerms = searchQuery.split(',').map(term => term.trim()).filter(term => term.length > 0);
    
    if (searchTerms.length === 0) {
      toast.error('Please enter valid search terms');
      return;
    }

    setIsLoading(true);
    setSearchResults([]);

    // If multiple terms, show different message
    if (searchTerms.length > 1) {
      toast.info(`Searching for ${searchTerms.length} terms: ${searchTerms.join(', ')}`);
    }

    // Set a timeout to reset loading state if no results are received
    const searchTimeout = setTimeout(() => {
      if (isLoading) {
        console.warn('AmazonSearch: Search timeout reached, no results received');
        toast.error('Search timed out. Please try again or check the extension.');
        setIsLoading(false);
      }
    }, 45000); // Increased timeout for multiple searches

    console.log(`AmazonSearch: Sending 'startSearch' to extension ID: ${extensionInfo.id} with ${searchTerms.length} terms`);
    try {
      // Send all search terms to extension for batch processing
      const response = await sendMessageToExtension({
        action: 'startSearch',
        searchTerms: searchTerms, // Send array of terms
        searchTerm: searchTerms[0] // Keep backward compatibility
      });

      if (response?.error) {
        toast.error(`Search failed: ${response.error}`);
        setIsLoading(false);
        clearTimeout(searchTimeout);
      } else if (response?.success) {
        const termText = searchTerms.length > 1 ? `${searchTerms.length} search terms` : `"${searchTerms[0]}"`;
        toast.success(`Search initiated for ${termText}. Results will appear shortly.`);
        // Don't set isLoading to false here - we'll wait for results
        // The timeout will handle the case where no results are received
      } else {
         toast.warning('Received an unexpected response from the extension after search initiation.');
         setIsLoading(false);
         clearTimeout(searchTimeout);
      }
    } catch (error: any) {
      console.error('Error initiating Amazon search:', error);
      toast.error(`Search failed: ${error.message || 'Communication error with extension'}`);
      setIsLoading(false);
      clearTimeout(searchTimeout);
    }
  };

  // Handle product selection
  const toggleProductSelection = (asin: string) => {
    const newSelection = new Set(selectedProducts);
    if (newSelection.has(asin)) {
      newSelection.delete(asin);
    } else {
      newSelection.add(asin);
    }
    setSelectedProducts(newSelection);
  };

  // Handle import button click
  const handleImport = () => {
    if (selectedProducts.size === 0) {
      toast.error('Please select at least one product to import');
      return;
    }

    setImportStep('import');

    // Initialize import properties for selected products
    const selectedProductsArray = Array.from(selectedProducts);
    const initialProperties: {[asin: string]: string} = {};
    const initialCollections: {[asin: string]: string} = {};
    const initialQuantities: {[asin: string]: number} = {};
    const initialMinQuantities: {[asin: string]: number} = {};

    selectedProductsArray.forEach(asin => {
      initialProperties[asin] = '';
      initialCollections[asin] = 'none'; // Use 'none' instead of empty string
      initialQuantities[asin] = 1;
      initialMinQuantities[asin] = 0;
    });

    setSelectedImportProperties(initialProperties);
    setSelectedImportCollections(initialCollections);
    setSelectedImportQuantities(initialQuantities);
    setSelectedImportMinQuantities(initialMinQuantities);
  };

  // Handle final import submission
  const handleFinalImport = () => {
    // Validate that all selected products have a property assigned
    const selectedProductsArray = Array.from(selectedProducts);
    const missingProperties = selectedProductsArray.filter(asin => !selectedImportProperties[asin]);

    if (missingProperties.length > 0) {
      toast.error(`Please assign a property to all selected products (${missingProperties.length} missing)`);
      return;
    }

    // Prepare import data
    const importItems: ExtensionImportProduct[] = selectedProductsArray.map(asin => {
      const product = searchResults.find(p => p.asin === asin);
      if (!product) return null;

      const propertyId = selectedImportProperties[asin];
      const property = properties.find(p => p.id === propertyId);

      // Convert 'none' to empty string for collection
      const collection = selectedImportCollections[asin] === 'none' ? '' : selectedImportCollections[asin];

      // Ensure all values are of the correct type
      const amazonUrl = String(product.url || `https://www.amazon.com/dp/${product.asin}`);
      const imageUrl = String(product.img || `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.title || 'Product')}`);

      console.log(`Preparing import for ${product.title}:`);
      console.log(`- Amazon URL: ${amazonUrl}`);
      console.log(`- Image URL: ${imageUrl}`);

      return {
        name: decodeHtmlEntities(String(product.title || 'Unnamed Product')),
        propertyId: String(propertyId || ''),
        propertyName: String(property?.name || 'Unknown Property'),
        collection: String(collection || ''),
        quantity: Number(selectedImportQuantities[asin] || 1),
        minQuantity: Number(selectedImportMinQuantities[asin] || 0),
        price: Number(product.numericPrice || 0),
        // Ensure we have a valid Amazon URL
        amazonUrl: amazonUrl,
        // Skip image processing
        hasProcessedImage: true,
        // Ensure we have a valid image URL
        imageUrl: imageUrl
      };
    }).filter(Boolean) as ExtensionImportProduct[];

    // Import the items
    if (importItems.length > 0) {
      try {
        // Stringify and parse to ensure we're not passing any complex objects
        const safeImportItems = JSON.parse(JSON.stringify(importItems));

        // Pass the items to the Inventory component via window message
        window.postMessage({
          type: "STAYFU_IMPORT_FROM_EXTENSION",
          data: safeImportItems,
          isExtensionImport: true
        }, window.location.origin);

        // Show a more detailed success message
        toast.success(`${importItems.length} products sent for import. Please wait while they are processed.`);

        // Clear localStorage after successful import
        localStorage.removeItem('amazon_search_results');
        localStorage.removeItem('amazon_import_step');
        localStorage.removeItem('amazon_search_query');

        // Close the import dialog immediately to avoid hanging
        // Reset state
        setImportStep('search');
        setSearchResults([]);
        setSelectedProducts(new Set());
        setSearchQuery('');
      } catch (error) {
        console.error('Error preparing import data:', error);
        toast.error('Error preparing import data. Please try again.');
      }
    } else {
      toast.error('No valid products to import');
    }
  };

  // Render search form
  const renderSearchForm = () => {
    return (
      <form onSubmit={handleSearch} className="space-y-4 p-6 bg-card rounded-lg border shadow-sm">
        <div className="space-y-2">
          <Label htmlFor="search">Search Query</Label>
          <Input
            id="search"
            placeholder="Enter product name or keywords (separate multiple terms with commas)..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <Button
          type="submit"
          className="w-full sm:w-auto flex items-center gap-2"
          disabled={isLoading || !searchQuery.trim()}
        >
          {isLoading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
              Searching...
            </>
          ) : (
            <>
              <Search className="h-4 w-4" />
              Search Amazon
            </>
          )}
        </Button>
      </form>
    );
  };

  // Render search results
  const renderSearchResults = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">
            Search Results ({filteredAndSortedResults.length} of {searchResults.length} products)
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <SlidersHorizontal className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setImportStep('search');
                setSearchResults([]);
                setSelectedProducts(new Set());
                // Clear localStorage when starting new search
                localStorage.removeItem('amazon_search_results');
                localStorage.removeItem('amazon_import_step');
                localStorage.removeItem('amazon_search_query');
                setSearchQuery('');
              }}
            >
              New Search
            </Button>
            <Button
              onClick={handleImport}
              disabled={selectedProducts.size === 0}
            >
              Import Selected ({selectedProducts.size})
            </Button>
          </div>
        </div>

        {/* Filter Controls */}
        <Collapsible open={showFilters} onOpenChange={setShowFilters}>
          <CollapsibleContent className="space-y-4">
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Search Term Filter */}
                <div className="space-y-2">
                  <Label htmlFor="searchTermFilter">Filter by Name/ASIN</Label>
                  <Input
                    id="searchTermFilter"
                    placeholder="Filter products..."
                    value={searchTermFilter}
                    onChange={(e) => setSearchTermFilter(e.target.value)}
                  />
                </div>

                {/* Price Range Filter */}
                <div className="space-y-2">
                  <Label>Price Range</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Min $"
                      type="number"
                      value={priceFilter.min}
                      onChange={(e) => setPriceFilter(prev => ({ ...prev, min: e.target.value }))}
                    />
                    <Input
                      placeholder="Max $"
                      type="number"
                      value={priceFilter.max}
                      onChange={(e) => setPriceFilter(prev => ({ ...prev, max: e.target.value }))}
                    />
                  </div>
                </div>

                {/* Rating Filter */}
                <div className="space-y-2">
                  <Label>Minimum Rating</Label>
                  <Select value={minRating.toString()} onValueChange={(value) => setMinRating(parseFloat(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Any Rating</SelectItem>
                      <SelectItem value="3">3+ Stars</SelectItem>
                      <SelectItem value="4">4+ Stars ⭐</SelectItem>
                      <SelectItem value="4.5">4.5+ Stars ⭐⭐</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort By */}
                <div className="space-y-2">
                  <Label>Sort By</Label>
                  <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="price-low">Price: Low to High</SelectItem>
                      <SelectItem value="price-high">Price: High to Low</SelectItem>
                      <SelectItem value="rating">Highest Rated</SelectItem>
                      <SelectItem value="reviews">Most Reviews</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Prime Filter & Clear */}
                <div className="space-y-2">
                  <Label>Filters</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="primeOnly"
                        checked={primeOnly}
                        onCheckedChange={(checked) => setPrimeOnly(checked === true)}
                      />
                      <Label htmlFor="primeOnly" className="text-sm">Prime Only</Label>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setPriceFilter({ min: '', max: '' });
                        setSearchTermFilter('');
                        setPrimeOnly(false);
                        setMinRating(0);
                        setSortBy('relevance');
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </CollapsibleContent>
        </Collapsible>

        {/* Active Filters Summary */}
        {(searchTermFilter || priceFilter.min || priceFilter.max || primeOnly || minRating > 0 || sortBy !== 'relevance') && (
          <div className="flex flex-wrap gap-2 p-3 bg-muted rounded-lg">
            <span className="text-sm font-medium">Active filters:</span>
            {searchTermFilter && (
              <Badge variant="secondary">
                Text: "{searchTermFilter}"
                <button 
                  onClick={() => setSearchTermFilter('')}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
            {priceFilter.min && (
              <Badge variant="secondary">
                Min: ${priceFilter.min}
                <button 
                  onClick={() => setPriceFilter(prev => ({ ...prev, min: '' }))}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
            {priceFilter.max && (
              <Badge variant="secondary">
                Max: ${priceFilter.max}
                <button 
                  onClick={() => setPriceFilter(prev => ({ ...prev, max: '' }))}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
            {minRating > 0 && (
              <Badge variant="secondary">
                {minRating}+ Stars ⭐
                <button 
                  onClick={() => setMinRating(0)}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
            {primeOnly && (
              <Badge variant="secondary">
                Prime Only
                <button 
                  onClick={() => setPrimeOnly(false)}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
            {sortBy !== 'relevance' && (
              <Badge variant="secondary">
                Sort: {sortBy === 'price-low' ? 'Price ↑' : sortBy === 'price-high' ? 'Price ↓' : sortBy === 'rating' ? 'Rating ↓' : sortBy === 'reviews' ? 'Reviews ↓' : sortBy}
                <button 
                  onClick={() => setSortBy('relevance')}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        )}

        {/* Products Grid */}
        {filteredAndSortedResults.length === 0 ? (
          <Card className="p-8">
            <div className="text-center text-muted-foreground">
              <Filter className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No products match your filters</h3>
              <p className="text-sm">
                {searchResults.length > 0 
                  ? "Try adjusting your filters or clearing them to see all results."
                  : "No search results to filter."
                }
              </p>
              {(searchTermFilter || priceFilter.min || priceFilter.max || primeOnly || sortBy !== 'relevance') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPriceFilter({ min: '', max: '' });
                    setSearchTermFilter('');
                    setPrimeOnly(false);
                    setSortBy('relevance');
                  }}
                  className="mt-3"
                >
                  Clear All Filters
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAndSortedResults.map((product, index) => (
            <Card key={`${product.asin}-${index}`} className={`overflow-hidden relative ${selectedProducts.has(product.asin) ? 'ring-2 ring-primary' : ''}`}>
              <div className="absolute top-3 right-3 z-10 bg-background/80 backdrop-blur-sm rounded-md p-1 shadow-sm">
                <Checkbox
                  checked={selectedProducts.has(product.asin)}
                  onCheckedChange={() => toggleProductSelection(product.asin)}
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
              </div>
              <div className="aspect-video relative bg-muted">
                <img
                  src={product.img}
                  alt={product.title || 'Product image'}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // Use a data URL for the fallback instead of external service
                    target.src = `data:image/svg+xml;base64,${btoa(`
                      <svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="300" height="300" fill="#f1f5f9"/>
                        <text x="150" y="140" font-family="Arial, sans-serif" font-size="14" fill="#64748b" text-anchor="middle">
                          ${(product.title?.slice(0, 20) || 'Product').replace(/[<>&"]/g, '')}
                        </text>
                        <text x="150" y="160" font-family="Arial, sans-serif" font-size="12" fill="#94a3b8" text-anchor="middle">
                          Image not available
                        </text>
                      </svg>
                    `)}`;
                  }}
                />
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium line-clamp-2 mb-2" title={product.title}>
                  {product.title || 'Unnamed Product'}
                </h3>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-lg font-bold">
                    {product.price || '$0.00'}
                  </span>
                  {product.isPrime && (
                    <Badge variant="outline">Prime</Badge>
                  )}
                </div>
                <div className="text-sm text-muted-foreground mb-1">
                  {(typeof product.rating === 'number' && product.rating > 0) ? (
                    <>⭐ {product.rating.toFixed(1)} stars ({(typeof product.reviewCount === 'number' && product.reviewCount > 0) ? product.reviewCount.toLocaleString() : 'No'} reviews)</>
                  ) : (
                    <span className="text-muted-foreground">No rating available</span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  Search: {product.searchTerm || 'Unknown'} • ASIN: {product.asin || 'Unknown'}
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const amazonUrl = product.url || `https://www.amazon.com/dp/${product.asin}`;
                    if (amazonUrl && product.asin) {
                      window.open(amazonUrl, '_blank');
                    } else {
                      toast.error('Product URL not available');
                    }
                  }}
                  disabled={!product.asin}
                >
                  View on Amazon
                </Button>
                <Button
                  size="sm"
                  onClick={() => toggleProductSelection(product.asin)}
                  disabled={!product.asin}
                >
                  {selectedProducts.has(product.asin) ? 'Deselect' : 'Select'}
                </Button>
              </CardFooter>
            </Card>
          ))}
          </div>
        )}
      </div>
    );
  };

  // Render import form
  const renderImportForm = () => {
    const selectedProductsArray = Array.from(selectedProducts);

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">
            Import Products ({selectedProductsArray.length} selected)
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setImportStep('results')}
            >
              Back to Results
            </Button>
            <Button
              onClick={handleFinalImport}
            >
              Complete Import
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {selectedProductsArray.map(asin => {
            const product = searchResults.find(p => p.asin === asin);
            if (!product) return null;

            return (
              <Card key={asin} className="overflow-hidden">
                <div className="flex flex-col md:flex-row">
                  <div className="w-full md:w-1/4 p-4">
                    <img
                      src={product.img}
                      alt={product.title}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                  <div className="w-full md:w-3/4 p-4">
                    <h3 className="font-medium mb-2">{product.title}</h3>
                    <p className="text-lg font-bold mb-4">{product.price}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`property-${asin}`}>Property *</Label>
                        <Select
                          value={selectedImportProperties[asin]}
                          onValueChange={async (value) => {
                            setSelectedImportProperties({
                              ...selectedImportProperties,
                              [asin]: value
                            });
                            
                            // Clear the collection selection when property changes
                            setSelectedImportCollections({
                              ...selectedImportCollections,
                              [asin]: ''
                            });
                            
                            // Fetch collections for the selected property
                            if (value) {
                              await fetchCollectionsForProperty(value);
                            }
                          }}
                        >
                          <SelectTrigger id={`property-${asin}`}>
                            <SelectValue placeholder="Select a property" />
                          </SelectTrigger>
                          <SelectContent>
                            {properties.map(property => (
                              <SelectItem key={property.id} value={property.id}>
                                {property.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`collection-${asin}`}>Collection (Optional)</Label>
                        <Select
                          value={selectedImportCollections[asin]}
                          onValueChange={(value) => {
                            setSelectedImportCollections({
                              ...selectedImportCollections,
                              [asin]: value
                            });
                          }}
                        >
                          <SelectTrigger id={`collection-${asin}`}>
                            <SelectValue placeholder="Select a collection" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            {(() => {
                              const propertyId = selectedImportProperties[asin];
                              const propertyCollections = propertyId ? propertyCollectionsMap[propertyId] || [] : [];
                              
                              return propertyCollections.length > 0 
                                ? propertyCollections.map(collection => (
                                    <SelectItem key={collection} value={collection}>
                                      {collection}
                                    </SelectItem>
                                  ))
                                : ['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Outdoor', 'Other'].map(collection => (
                                    <SelectItem key={collection} value={collection}>
                                      {collection}
                                    </SelectItem>
                                  ));
                            })()}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`quantity-${asin}`}>Quantity</Label>
                        <Input
                          id={`quantity-${asin}`}
                          type="number"
                          min="1"
                          value={selectedImportQuantities[asin] || 1}
                          onChange={(e) => {
                            setSelectedImportQuantities({
                              ...selectedImportQuantities,
                              [asin]: parseInt(e.target.value) || 1
                            });
                          }}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`min-quantity-${asin}`}>Min Quantity</Label>
                        <Input
                          id={`min-quantity-${asin}`}
                          type="number"
                          min="0"
                          value={selectedImportMinQuantities[asin] || 0}
                          onChange={(e) => {
                            setSelectedImportMinQuantities({
                              ...selectedImportMinQuantities,
                              [asin]: parseInt(e.target.value) || 0
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  // Render content based on extension status and current step
  const renderContent = () => {
    if (isCheckingExtension) {
      return (
        <Alert>
          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
          <AlertDescription>
            Checking for StayFu Chrome Extension...
          </AlertDescription>
        </Alert>
      );
    }

    if (!isExtensionInstalled) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Extension Not Found</AlertTitle>
          <AlertDescription className="space-y-2">
             <p>
               The StayFu Chrome Extension was not detected. Please ensure it's installed and enabled in your browser.
               {extensionInfo?.id && ` (Tried extension ID: ${extensionInfo.id})`}
             </p>
             <div className="pt-2">
               <Button
                 variant="outline"
                 size="sm"
                 onClick={checkExtension}
                 className="mr-2"
               >
                 Check Again
               </Button>
               <Button
                 variant="outline"
                 size="sm"
                 onClick={() => window.open('https://docs.stayfu.com/extension', '_blank')}
               >
                 Installation Guide
               </Button>
             </div>
          </AlertDescription>
        </Alert>
      );
    }

    if (!isExtensionConnected) {
      return (
        <div className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Extension Not Connected</AlertTitle>
            <AlertDescription className="space-y-2">
              <p>
                The StayFu Chrome Extension is installed but not properly connected to the application.
                You can auto-configure it below or manually configure in extension settings.
              </p>
              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={checkExtension}
                  className="mr-2"
                >
                  Check Again
                </Button>
                <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (chrome.runtime.openOptionsPage) {
                    chrome.runtime.openOptionsPage();
                  } else {
                    window.open(chrome.runtime.getURL('options.html'));
                  }
                }}
              >
                Open Extension Settings
              </Button>
            </div>
          </AlertDescription>
        </Alert>

        <ExtensionAutoConfig />
      </div>
    );
  }

  // Extension is installed and connected, show the appropriate step
    if (importStep === 'search') {
      return renderSearchForm();
    } else if (importStep === 'results') {
      return renderSearchResults();
    } else if (importStep === 'import') {
      return renderImportForm();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">
          Search Amazon products to import into your inventory via the StayFu Chrome Extension
        </h2>
      </div>

      <div className="glass p-6 rounded-xl space-y-4">
        {renderContent()}
      </div>
    </div>
  );
};

export default AmazonSearch;
