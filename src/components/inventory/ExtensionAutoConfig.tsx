import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, Loader2, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { sendMessageToExtension } from '@/utils/extensionDetection';

const ExtensionAutoConfig = () => {
  const { authState } = useAuth();
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [configStatus, setConfigStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleAutoConfig = async () => {
    if (!authState?.user) {
      toast.error('Please log in to configure the extension');
      return;
    }

    setIsConfiguring(true);
    setConfigStatus('idle');

    try {
      // Step 1: Generate API token
      toast.info('Generating API token...');
      
      const { data, error } = await supabase.functions.invoke('generate-extension-token', {
        headers: {
          Authorization: `Bearer ${authState.session?.access_token}`,
        },
      });

      if (error) {
        throw new Error(`Failed to generate token: ${error.message}`);
      }

      if (!data?.token) {
        throw new Error('No token received from server');
      }

      // Step 2: Configure extension
      toast.info('Configuring extension...');
      
      const response = await sendMessageToExtension({
        action: 'autoConfig',
        config: {
          url: window.location.origin,
          token: data.token,
          userId: authState.user.id
        }
      });

      if (response?.success) {
        setConfigStatus('success');
        toast.success('Extension configured successfully! You can now use Amazon search.');
      } else {
        throw new Error(response?.error || 'Extension configuration failed');
      }

    } catch (error: any) {
      console.error('Auto-config error:', error);
      setConfigStatus('error');
      toast.error(`Configuration failed: ${error.message}`);
    } finally {
      setIsConfiguring(false);
    }
  };

  return (
    <div className="space-y-4">
      <Alert>
        <Settings className="h-4 w-4" />
        <AlertDescription>
          Auto-configure the Chrome extension with your account credentials for seamless Amazon product imports.
        </AlertDescription>
      </Alert>

      <Button 
        onClick={handleAutoConfig}
        disabled={isConfiguring || !authState?.user}
        className="w-full"
      >
        {isConfiguring ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Configuring Extension...
          </>
        ) : (
          <>
            <Settings className="mr-2 h-4 w-4" />
            Auto-Configure Extension
          </>
        )}
      </Button>

      {configStatus === 'success' && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-green-600">
            ✅ Extension configured successfully! You can now search and import Amazon products.
          </AlertDescription>
        </Alert>
      )}

      {configStatus === 'error' && (
        <Alert variant="destructive">
          <AlertDescription>
            ❌ Configuration failed. You may need to manually configure the extension in its settings.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default ExtensionAutoConfig;
