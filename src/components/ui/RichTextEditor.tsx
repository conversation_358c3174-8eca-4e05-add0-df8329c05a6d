import React, { useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  height?: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  height = 400,
  placeholder = "Start typing...",
  disabled = false,
  className = ""
}) => {
  const editorRef = useRef<any>(null);

  const handleEditorChange = (content: string) => {
    onChange(content);
  };

  return (
    <div className={className}>
      <Editor
        onInit={(evt, editor) => editorRef.current = editor}
        value={value}
        onEditorChange={handleEditorChange}
        disabled={disabled}
        apiKey="i6ak3rk944fx6vz4yzerqbdbun33pd517b1ul467shfw5yf5"
        init={{
          height: height,
          menubar: false,
          // Set base URL for TinyMCE assets
          base_url: '/tinymce',
          suffix: '.min',
          plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
          ],
          toolbar: 'undo redo | blocks | ' +
            'bold italic forecolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | help',
          content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
          placeholder: placeholder,
          skin: 'oxide',
          content_css: 'default',
          branding: false,
          resize: true,
          statusbar: false,
          elementpath: false,
          // Configure for better accessibility
          a11y_advanced_options: true,
          // Prevent issues with React strict mode
          setup: (editor) => {
            editor.on('init', () => {
              // Ensure proper initialization
              if (value && value !== editor.getContent()) {
                editor.setContent(value);
              }
            });
          }
        }}
      />
    </div>
  );
};

export default RichTextEditor;
