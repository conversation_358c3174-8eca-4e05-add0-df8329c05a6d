import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Sparkles, MessageCircle } from 'lucide-react';
import { useFloatingAi } from '@/contexts/FloatingAiContext';
import { cn } from '@/lib/utils';

interface FloatingAiToggleProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showLabel?: boolean;
}

const FloatingAiToggle: React.FC<FloatingAiToggleProps> = ({
  className,
  variant = 'default',
  size = 'default',
  showLabel = true
}) => {
  const { state, toggleVisibility } = useFloatingAi();
  
  const hasUnreadMessages = state.messages.length > 0 && !state.isVisible;
  
  return (
    <Button
      onClick={toggleVisibility}
      variant={variant}
      size={size}
      className={cn(
        "relative",
        state.isVisible && "bg-primary/10 border-primary/20",
        className
      )}
      title={state.isVisible ? "Hide AI Assistant" : "Show AI Assistant"}
    >
      <div className="flex items-center gap-2">
        <div className="relative">
          <Sparkles className={cn(
            "h-4 w-4",
            state.isVisible ? "text-primary" : "text-muted-foreground"
          )} />
          {hasUnreadMessages && (
            <div className="absolute -top-1 -right-1 h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
          )}
        </div>
        {showLabel && (
          <span className="text-sm font-medium">
            AI Assistant
          </span>
        )}
      </div>
      
      {state.currentError && (
        <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">!</span>
        </div>
      )}
      
      {state.followUpQuestions.length > 0 && (
        <div className="absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">?</span>
        </div>
      )}
    </Button>
  );
};

export default FloatingAiToggle;
