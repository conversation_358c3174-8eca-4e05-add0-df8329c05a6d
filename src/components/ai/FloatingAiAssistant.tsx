import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Loader2,
  Send,
  Sparkles,
  AlertCircle,
  Info,
  Mic,
  RotateCcw,
  MessageCircle,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { showAIConfirmationToast } from '@/lib/utils';
import { useFloatingAi } from '@/contexts/FloatingAiContext';
import { createSpeechRecognition, isSpeechRecognitionSupported, getBrowserSpeechSupport, type SpeechRecognitionPolyfill } from '@/utils/speechRecognitionPolyfill';
import { processEnhancedCommand, handleAIError, generateSmartFollowUp } from '@/utils/enhancedAiAssistant';
import { getCommandExamples } from '@/utils/aiErrorSuggestions';
import { cn } from '@/lib/utils';

const FloatingAiAssistant: React.FC = () => {
  const [command, setCommand] = useState<string>('');
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognitionPolyfill | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { authState } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    state,
    toggleMinimized,
    addMessage,
    clearConversation,
    getConversationContext,
    setError,
    addFollowUpQuestion,
    clearFollowUpQuestions,
    removeFollowUpQuestion,
    setLoading,
    setLastCommand
  } = useFloatingAi();
  
  const userId = authState.user?.id;

  // Auto-focus input when assistant becomes visible
  useEffect(() => {
    if (!state.isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [state.isMinimized]);

  const handleCommandChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCommand(e.target.value);
    // Clear error when user starts typing
    if (state.currentError) {
      setError(null);
    }
  };

  const handleAudioInput = async () => {
    if (!isSpeechRecognitionSupported()) {
      const browserSupport = getBrowserSpeechSupport();
      toast.error(`Speech recognition not supported. Browser: ${browserSupport.browser}`);
      return;
    }

    if (isRecording) {
      recognitionRef.current?.stop();
      setIsRecording(false);
      return;
    }

    try {
      const recognition = createSpeechRecognition();
      if (!recognition) {
        toast.error('Failed to create speech recognition instance');
        return;
      }

      recognitionRef.current = recognition;

      recognition.onstart = () => {
        setIsRecording(true);
        toast.info("Listening... Speak your command");
      };

      recognition.onresult = (event) => {
        const transcript = event.results[0]?.transcript || '';
        setCommand(transcript);
        toast.success(`Heard: "${transcript}"`);
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        toast.error(`Speech recognition error: ${event.error}`);
        setIsRecording(false);
      };

      recognition.onend = () => {
        setIsRecording(false);
      };

      recognition.start();
    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      toast.error('Failed to start speech recognition');
      setIsRecording(false);
    }
  };

  const handleFollowUpResponse = async (questionId: string, response: string, context: string) => {
    removeFollowUpQuestion(questionId);
    
    // Generate enhanced command from follow-up response
    const enhancedCommand = generateSmartFollowUp(response, context, state.lastCommand);
    setCommand(enhancedCommand);
    
    // Auto-submit the enhanced command
    setTimeout(() => {
      handleSubmit(null, enhancedCommand);
    }, 100);
  };

  const handleSubmit = async (e: React.FormEvent | null, overrideCommand?: string) => {
    if (e) e.preventDefault();
    
    const currentCommand = overrideCommand || command.trim();
    if (!currentCommand) return;
    
    if (!userId) {
      toast.error("You must be logged in to use the AI assistant");
      return;
    }

    setLoading(true);
    setLastCommand(currentCommand);
    setError(null);
    clearFollowUpQuestions();

    // Process with enhanced AI logic
    const conversationHistory = state.messages.slice(-5).map(m => m.content);
    const enhancedResult = processEnhancedCommand(currentCommand, conversationHistory);

    // Add user message to conversation
    addMessage({
      type: 'user',
      content: currentCommand,
      timestamp: new Date()
    });

    if (!enhancedResult.shouldProceedToBackend) {
      // Handle locally with enhanced feedback
      setLoading(false);
      
      if (enhancedResult.error) {
        setError(enhancedResult.error);
      }
      
      if (enhancedResult.followUpQuestions) {
        enhancedResult.followUpQuestions.forEach(question => {
          addFollowUpQuestion(question);
        });
      }
      
      addMessage({
        type: 'ai',
        content: enhancedResult.message,
        timestamp: new Date()
      });
      
      setCommand('');
      return;
    }

    try {
      console.log("Sending command to AI:", currentCommand);

      // Get conversation context for better AI understanding
      const conversationContext = getConversationContext();
      const enhancedCommand = conversationContext ? `${conversationContext} ${currentCommand}` : currentCommand;

      // Call Supabase edge function
      const { data, error } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command: enhancedCommand,
          userId,
          originalCommand: currentCommand,
          hasContext: conversationContext.length > 0
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to process command');
      }

      if (!data) {
        throw new Error('No response received from AI command processor');
      }

      console.log("Response from AI command processor:", data);

      // Add AI response to conversation
      addMessage({
        type: 'ai',
        content: data.message || 'Command processed successfully',
        timestamp: new Date(),
        result: data
      });

      // Show confirmation toast
      setTimeout(() => {
        showAIConfirmationToast(data, {
          entityType: data.entityType || '',
          entityId: data.entityId,
          navigate
        });
      }, 0);

    } catch (error) {
      console.error("Error processing AI command:", error);
      
      const aiError = handleAIError(error as Error, currentCommand, conversationHistory);
      setError(aiError);
      
      addMessage({
        type: 'ai',
        content: `Error: ${aiError.message}`,
        timestamp: new Date()
      });
      
      toast.error(aiError.message);
    } finally {
      setLoading(false);
      setCommand('');
    }
  };

  const getPlaceholderText = () => {
    const currentPath = location.pathname;

    // Context-aware suggestions based on current page
    let suggestions: string[] = [];

    if (currentPath.includes('/properties')) {
      suggestions = [
        "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Update the Beach House property to have 4 bedrooms instead of 3"
      ];
    } else if (currentPath.includes('/maintenance')) {
      suggestions = [
        "Create a maintenance task to fix the broken sink at Beach House",
        "Assign the HVAC repair task to John Smith",
        "Mark the plumbing task as completed at Ocean View"
      ];
    } else if (currentPath.includes('/inventory')) {
      suggestions = [
        "We're down to only 2 bath towels, we need a minimum of 12",
        "Add 10 wine glasses to the Beach House kitchen collection",
        "Create a purchase order for all low stock items"
      ];
    } else if (currentPath.includes('/damages')) {
      suggestions = [
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Update the carpet damage at beach house status to resolved",
        "Generate an invoice for the Beach House carpet repair"
      ];
    } else if (currentPath.includes('/purchase-orders')) {
      suggestions = [
        "Create a purchase order for all low stock items",
        "Add cleaning supplies to the pending purchase order",
        "Mark the towel order as received"
      ];
    } else {
      // Default dashboard suggestions
      suggestions = [
        "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Create a maintenance task to fix the broken sink at Beach House",
        "Create a purchase order for all low stock items",
        "We're down to only 2 bath towels, we need a minimum of 12"
      ];
    }

    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  // FloatingAiAssistant is always visible when rendered

  return (
    <div
      className={cn(
        "fixed left-0 right-0 bottom-0 z-50 bg-background/95 backdrop-blur-sm border-t border-border shadow-lg transition-all duration-300",
        state.isMinimized ? "h-12" : "h-auto"
      )}
    >
      <div className="max-w-7xl mx-auto px-4">
        {/* Header Bar */}
        <div className="flex items-center justify-between h-12">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">AI Assistant</span>
            {state.isLoading && (
              <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
            )}
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={toggleMinimized}
              className="h-8 w-8 p-0"
              title={state.isMinimized ? 'Expand' : 'Minimize'}
            >
              {state.isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Main Content */}
        {!state.isMinimized && (
          <div className="pb-4 space-y-3">
            {/* Command Input */}
            <form onSubmit={handleSubmit} className="flex gap-2">
              <button
                type="button"
                onClick={() => {
                  setCommand('help');
                  setShowSuggestions(true);
                }}
                disabled={state.isLoading}
                className="flex items-center gap-1 px-3 py-2 bg-muted/30 rounded-md hover:bg-muted/50 transition-colors cursor-pointer disabled:cursor-not-allowed"
                title="Click for AI help and examples"
              >
                <Sparkles className="h-3 w-3 text-primary" />
                <span className="text-xs font-medium text-muted-foreground">Help</span>
              </button>
              
              <Input
                ref={inputRef}
                placeholder={`Try: "${getPlaceholderText()}"`}
                value={command}
                onChange={handleCommandChange}
                className="flex-1 text-sm"
                disabled={state.isLoading}
              />
              
              <Button 
                type="button" 
                size="sm" 
                onClick={handleAudioInput} 
                disabled={state.isLoading}
                variant="outline"
              >
                {isRecording ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Mic className="h-4 w-4" />
                )}
              </Button>
              
              {state.messages.length > 0 && (
                <>
                  <Button 
                    type="button" 
                    size="sm" 
                    onClick={() => setShowHistory(!showHistory)}
                    disabled={state.isLoading}
                    variant="outline"
                  >
                    <MessageCircle className="h-4 w-4" />
                  </Button>
                  
                  <Button 
                    type="button" 
                    size="sm" 
                    onClick={() => {
                      clearConversation();
                      toast.success('Conversation cleared');
                    }}
                    disabled={state.isLoading}
                    variant="outline"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              <Button 
                type="submit" 
                size="sm" 
                disabled={!command.trim() || state.isLoading}
              >
                {state.isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </form>

            {/* Error Display */}
            {state.currentError && (
              <div className="p-3 bg-red-50 dark:bg-red-950/20 rounded-md border border-red-200 dark:border-red-800">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-sm text-red-700 dark:text-red-300 font-medium">
                      {state.currentError.message}
                    </p>
                    {state.currentError.suggestions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        <p className="text-xs text-red-600 dark:text-red-400 font-medium">Suggestions:</p>
                        <ul className="text-xs text-red-600 dark:text-red-400 space-y-1">
                          {state.currentError.suggestions.map((suggestion, index) => (
                            <li key={index} className="flex items-start gap-1">
                              <span className="text-red-400">•</span>
                              {suggestion.startsWith('Try:') ? (
                                <button
                                  type="button"
                                  onClick={() => {
                                    const commandText = suggestion.replace(/^Try:\s*"?|"?$/g, '');
                                    setCommand(commandText);
                                  }}
                                  className="text-left hover:underline cursor-pointer text-blue-600 dark:text-blue-400"
                                >
                                  {suggestion}
                                </button>
                              ) : (
                                <span>{suggestion}</span>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Follow-up Questions */}
            {state.followUpQuestions.length > 0 && (
              <div className="space-y-2">
                {state.followUpQuestions.map((question) => (
                  <div key={question.id} className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md border border-blue-200 dark:border-blue-800">
                    <div className="flex items-start gap-2">
                      <Info className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <p className="text-sm text-blue-700 dark:text-blue-300 font-medium mb-2">
                          {question.question}
                        </p>
                        {question.options ? (
                          <div className="flex flex-wrap gap-2">
                            {question.options.map((option, index) => (
                              <Button
                                key={index}
                                size="sm"
                                variant="outline"
                                onClick={() => handleFollowUpResponse(question.id, option, question.context)}
                                className="h-7 px-2 text-xs"
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        ) : (
                          <Input
                            placeholder="Type your answer..."
                            className="text-sm"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                const target = e.target as HTMLInputElement;
                                if (target.value.trim()) {
                                  handleFollowUpResponse(question.id, target.value.trim(), question.context);
                                }
                              }
                            }}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Conversation History */}
            {showHistory && state.messages.length > 0 && (
              <div className="p-3 bg-muted/30 rounded-md border max-h-48 overflow-y-auto">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium">Conversation History</h4>
                  <button
                    type="button"
                    onClick={() => setShowHistory(false)}
                    className="text-xs text-muted-foreground hover:text-foreground"
                  >
                    Close
                  </button>
                </div>
                <div className="space-y-2">
                  {state.messages.slice(-10).map((msg) => (
                    <div key={msg.id} className="text-xs">
                      <div className="flex items-center justify-between mb-1">
                        <span className={cn(
                          "font-medium",
                          msg.type === 'user' ? "text-blue-600 dark:text-blue-400" : "text-green-600 dark:text-green-400"
                        )}>
                          {msg.type === 'user' ? 'You' : 'AI'}
                        </span>
                        <span className="text-muted-foreground">
                          {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                      <p className="leading-tight text-muted-foreground">{msg.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Command Suggestions */}
            {showSuggestions && (
              <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300">Command Suggestions</h4>
                  <button
                    type="button"
                    onClick={() => setShowSuggestions(false)}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Close
                  </button>
                </div>
                <div className="space-y-2">
                  {getCommandExamples(location.pathname).map((example, index) => (
                    <button
                      type="button"
                      key={index}
                      onClick={() => {
                        setCommand(example);
                        setShowSuggestions(false);
                      }}
                      className="block w-full text-left text-xs p-2 rounded bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors text-blue-700 dark:text-blue-300"
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingAiAssistant;
