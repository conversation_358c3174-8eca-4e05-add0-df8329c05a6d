# AI Inventory Management Scenarios

## All Possible Scenarios the AI Could Encounter

### 1. **Item Doesn't Exist Anywhere**
- **Command**: "add 3 towels to Beach House"
- **Current State**: No "towels" in any property's inventory
- **Expected Action**: Create new inventory item with name="towels", quantity=3, property="Beach House"
- **AI Action**: `addInventoryItem`

### 2. **Item Exists in Target Property**
- **Command**: "add 3 towels to Beach House" 
- **Current State**: "towels" already exists in Beach House inventory (quantity=5)
- **Expected Action**: Update existing item quantity (5 + 3 = 8)
- **AI Action**: `updateInventoryItem` with increaseQuantity=3

### 3. **Item Exists in Other Properties, Not Target Property**
- **Command**: "add 3 towels to Beach House"
- **Current State**: "towels" exists in Ocean View (quantity=10) but NOT in Beach House
- **Expected Action**: Copy the item template from Ocean View and create new item in Beach House
- **AI Action**: `copyInventoryItemToProperty` (NEW FUNCTION NEEDED)

### 4. **Similar Item Exists (Fuzzy Match)**
- **Command**: "add 3 bath towels to Beach House"
- **Current State**: "towels" exists in Beach House, no "bath towels"
- **Expected Action**: Ask user if they meant "towels" or create new "bath towels"
- **AI Action**: Intelligent suggestion with options

### 5. **Property Doesn't Exist**
- **Command**: "add 3 towels to Nonexistent Property"
- **Current State**: Property "Nonexistent Property" doesn't exist
- **Expected Action**: Suggest available properties
- **AI Action**: Error with property suggestions

### 6. **Ambiguous Quantity Commands**
- **Command**: "we need more towels at Beach House"
- **Current State**: "towels" exists in Beach House (quantity=2)
- **Expected Action**: Increase quantity by reasonable amount (e.g., 5-10)
- **AI Action**: `updateInventoryItem` with smart quantity increase

### 7. **Set Quantity Commands**
- **Command**: "set towels to 15 at Beach House"
- **Current State**: "towels" exists in Beach House (quantity=5)
- **Expected Action**: Set quantity to exactly 15
- **AI Action**: `updateInventoryItem` with quantity=15

### 8. **Minimum Quantity Commands**
- **Command**: "we need at least 10 towels at Beach House"
- **Current State**: "towels" exists in Beach House (quantity=3, min_quantity=5)
- **Expected Action**: Set quantity=10, min_quantity=10
- **AI Action**: `updateInventoryItem` with both values

### 9. **Multiple Properties Command**
- **Command**: "add 5 towels to all properties"
- **Current State**: Multiple properties exist
- **Expected Action**: Add/update towels in each property
- **AI Action**: Multiple operations or batch function

### 10. **Collection-Specific Commands**
- **Command**: "add luxury towels to bathroom collection at Beach House"
- **Current State**: Bathroom collection exists
- **Expected Action**: Create item with specific collection
- **AI Action**: `addInventoryItem` with collection="bathroom"

## Current Issues Identified

### Issue 1: AI Not Parsing Item Names Correctly
- **Problem**: "add 1 fly swatter to Thames inventory" → AI says "item name missing"
- **Root Cause**: AI prompt parsing or validation logic issue
- **Fix**: Enhance AI prompt and validation

### Issue 2: No Copy Functionality
- **Problem**: Item exists in Property A, user wants to add to Property B
- **Current Behavior**: Creates new item or fails
- **Needed**: Copy item template (name, collection, min_quantity, price, urls) to new property

### Issue 3: Poor Error Messages
- **Problem**: Generic error messages don't help users
- **Current**: "I didn't find towels in your inventory"
- **Needed**: "No towels found in Thames inventory. I found towels in Ocean View - would you like me to copy them to Thames?"

### Issue 4: No Fuzzy Matching Suggestions
- **Problem**: "bath towels" vs "towels" should suggest alternatives
- **Needed**: Smart suggestions for similar items

## Required New Functions

### 1. `copyInventoryItemToProperty()`
```typescript
// Copy an existing item from one property to another
async function copyInventoryItemToProperty(supabase, userId, sourceItem, targetPropertyId, quantity)
```

### 2. Enhanced `findSimilarItems()`
```typescript
// Find similar items across all properties with better matching
async function findSimilarItemsAcrossProperties(supabase, userId, searchTerm, targetProperty)
```

### 3. `handleSmartInventoryAdd()`
```typescript
// Intelligent handler that decides whether to add, update, or copy
async function handleSmartInventoryAdd(supabase, userId, data)
```

## Implementation Priority

1. **HIGH**: Fix AI parsing issue (item name not recognized)
2. **HIGH**: Implement copy functionality for existing items
3. **MEDIUM**: Enhanced error messages with suggestions
4. **MEDIUM**: Fuzzy matching and smart suggestions
5. **LOW**: Batch operations for multiple properties
