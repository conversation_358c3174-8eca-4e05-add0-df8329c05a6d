# Comprehensive User Commands for Property Management AI

## Property Management Commands
1. "Add a new property called Sunset Villa on 123 Main St"
2. "Create property Ocean Breeze with 3 bedrooms and 2 bathrooms"
3. "Update Beach House to have 4 bedrooms instead of 3"
4. "Set the budget for Mountain Cabin to $800"
5. "Add property photos for the downtown apartment"
6. "Change the address of Lakeside Retreat"
7. "Mark Seaside Cottage as occupied until next Friday"
8. "Set check-in time for Beach House to 3 PM"

## Maintenance Task Commands
9. "Fix the broken AC at Beach House"
10. "Create maintenance task for leaky faucet in unit 2B"
11. "Schedule HVAC inspection for all properties next week"
12. "Assign the plumbing repair to <PERSON>"
13. "Mark the electrical work as completed"
14. "Set priority to urgent for the heating issue"
15. "Create recurring maintenance for pool cleaning"
16. "The dishwasher at Ocean View needs repair"

## Inventory Management Commands
17. "We're down to 2 towels, need minimum of 12"
18. "Add 5 wine glasses to the kitchen collection"
19. "Update toilet paper quantity to 20 at Beach House"
20. "We need more cleaning supplies for the bathroom"
21. "Order dish soap for all properties"
22. "Set minimum stock of bed sheets to 8"
23. "Add new item: coffee maker to kitchen"
24. "Remove broken blender from inventory"

## Damage Report Commands
25. "Create damage report for wine stain on carpet"
26. "Report broken window at unit 3A"
27. "Document water damage in the basement"
28. "Update carpet damage status to resolved"
29. "Generate invoice for the kitchen repair"
30. "Mark the door damage as pending review"
31. "Create damage report for scratched hardwood floors"
32. "Report missing TV remote at Beach House"

## Purchase Order Commands
33. "Create purchase order for all low stock items"
34. "Order more towels and bed sheets"
35. "Buy new coffee machine for the kitchen"
36. "Purchase cleaning supplies for spring cleaning"
37. "Order replacement parts for the dishwasher"
38. "Create PO for bathroom renovation supplies"
39. "Buy emergency supplies for hurricane season"
40. "Order new furniture for the living room"

## Collection Management Commands
41. "Create new bathroom collection with $300 budget"
42. "Add items to the luxury amenities collection"
43. "Update kitchen collection budget to $500"
44. "Remove old items from bedroom collection"
45. "Create holiday decoration collection"

## Guest/Booking Related Commands
46. "Block Beach House calendar for maintenance"
47. "Set cleaning schedule after checkout"
48. "Update guest instructions for check-in"
49. "Create welcome package for VIP guests"
50. "Schedule deep cleaning between bookings"

## Financial/Budget Commands
51. "Track expenses for property maintenance"
52. "Set monthly budget for supplies"
53. "Generate cost report for last quarter"
54. "Update pricing for cleaning services"
55. "Calculate ROI for property improvements"

## Staff/Team Management Commands
56. "Assign cleaning crew to Beach House"
57. "Schedule team meeting for next Monday"
58. "Update contact info for maintenance staff"
59. "Create task list for new employee"
60. "Set vacation schedule for cleaning team"

## Emergency/Urgent Commands
61. "Emergency: water leak at Ocean View"
62. "Urgent repair needed for broken lock"
63. "Guest complaint about heating not working"
64. "Power outage at Mountain Cabin"
65. "Security issue at downtown property"

## Reporting/Analytics Commands
66. "Generate maintenance report for this month"
67. "Show inventory levels across all properties"
68. "Create damage summary for insurance"
69. "Export guest feedback data"
70. "Analyze cleaning supply usage trends"
