// content.js - Refactored for Manifest V3 - Version 2.0.0

console.log("StayFu Content Script Loaded.");

// --- Extension Announcement System ---
// Listen for requests from the web app to announce our extension
window.addEventListener('message', (event) => {
    if (event.data.type === 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT') {
        console.log('Content Script: Received announcement request from web app');
        
        try {
            // Check if extension context is valid before making runtime calls
            if (!chrome.runtime || !chrome.runtime.id) {
                console.warn('Content Script: Extension context not available');
                // Don't announce anything if context is invalid - this prevents issues
                return;
            }
            
            // Request extension info from background script
            chrome.runtime.sendMessage({ action: 'getExtensionInfo' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('Content Script: Error getting extension info:', chrome.runtime.lastError);
                    // Don't announce anything if we have errors - let the timeout handle it
                    return;
                }
                
                // Announce our extension to the web app
                window.postMessage({
                    type: 'STAYFU_EXTENSION_ANNOUNCEMENT',
                    extensionId: chrome.runtime.id,
                    connected: response?.connected === true,
                    version: response?.version || '2.0.0',
                    name: 'StayFu Chrome Extension',
                    timestamp: Date.now()
                }, '*');
            
            console.log('Content Script: Announced extension with ID:', chrome.runtime.id);
        });
        
        } catch (error) {
            console.error('Content Script: Error in announcement handler:', error);
            // Don't announce anything on error - let the timeout handle it
        }
    }
});

// Announce ourselves immediately when the content script loads
// This helps with faster detection
setTimeout(() => {
    try {
        // Check if extension context is valid
        if (!chrome.runtime || !chrome.runtime.id) {
            console.warn('Content Script: Extension context not available on load');
            return;
        }
        
        chrome.runtime.sendMessage({ action: 'getExtensionInfo' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('Content Script: Error getting extension info on load:', chrome.runtime.lastError);
                return;
            }
            
            window.postMessage({
                type: 'STAYFU_EXTENSION_ANNOUNCEMENT',
                extensionId: chrome.runtime.id,
                connected: response?.connected === true,
                version: response?.version || '2.0.0',
                name: 'StayFu Chrome Extension',
                timestamp: Date.now()
            }, '*');
        
        console.log('Content Script: Auto-announced extension with ID:', chrome.runtime.id);
    });
    
    } catch (error) {
        console.error('Content Script: Error in immediate announcement:', error);
    }
}, 1000); // Delay to ensure page is ready

// --- Global variables ---
const processedAsins = new Set(); // Keep track of processed ASINs to avoid duplicates
let searchTerm = ''; // Will be extracted from URL

// Extract search term from URL if present
try {
    searchTerm = decodeURIComponent((new URL(window.location.href)).searchParams.get('k')) || '';
    console.log("Extracted search term from URL:", searchTerm);
} catch (e) {
    console.error("Error extracting search term:", e);
}

// Load search term from storage if not in URL
if (!searchTerm) {
    chrome.storage.local.get(['currentSearch'], (data) => {
        if (data.currentSearch && data.currentSearch.term) {
            searchTerm = data.currentSearch.term;
            console.log("Loaded search term from storage:", searchTerm);
        }
    });
}

// --- Scraping Functions ---

// Helper function to format rating with proper Unicode stars
function formatRating(rating) {
    if (!rating) return '';
    const fullStars = '★'.repeat(Math.floor(rating));
    const halfStar = rating % 1 >= 0.5 ? '½' : '';
    const emptyStars = '☆'.repeat(5 - Math.ceil(rating));
    return fullStars + halfStar + emptyStars;
}

// Scrapes product data from a search results page
async function scrapeSearchResults() {
    console.log("Scraping search results for:", { searchTerm });
    const products = [];

    // More specific selector to avoid catching non-product elements
    const productDivs = document.querySelectorAll('.s-result-item[data-asin]');

    console.log(`Found ${productDivs.length} product divs on page with specific s-result-item selector`);

    // Process products sequentially to avoid overwhelming the browser
    for (const div of productDivs) {
        const asin = div.getAttribute('data-asin');

        // Skip if ASIN is missing or already processed when collecting all products
        if (!asin || processedAsins.has(asin)) {
            continue;
        }

        try {
            // Try various selectors to find the title element, from specific to more general
            const titleElement =
                div.querySelector('h2 a.a-link-normal span.a-text-normal') || // Most common specific structure
                div.querySelector('h2 a span') ||                   // Structure: h2 > a > span (fallback)
                div.querySelector('h2 span') ||                     // Span directly inside h2 (less common for title)
                div.querySelector('h2 a') ||                        // Link text inside h2 (if no inner span)
                div.querySelector('h2') ||                          // The h2 text itself (broadest)
                // Fallback to class-based selectors
                div.querySelector('span.a-size-medium.a-color-base.a-text-normal') ||
                div.querySelector('span.a-size-base-plus.a-color-base.a-text-normal');

            let title = titleElement ? titleElement.innerText.trim() : '';

            // Clean up title if needed - handle "Sponsored" text
            if (titleElement && titleElement.tagName === 'H2' && title) {
                // Try to find the most likely title span within the h2
                const innerSpan = titleElement.querySelector('span[dir="auto"]'); // Amazon often uses span[dir="auto"] for titles
                if (innerSpan && innerSpan.innerText) {
                    title = innerSpan.innerText.trim();
                }
                // Remove "Sponsored" prefix if present
                if (title.startsWith('Sponsored\n')) title = title.substring(10).trim();
            }

            // Get other product details
            const imgElement = div.querySelector('img.s-image');
            const originalImgUrl = imgElement ? imgElement.getAttribute('src') : '';

            // Process the image if available
            let img = originalImgUrl;
            let processedImageDataUrl = null;

            if (originalImgUrl && window.imageProcessor) {
                try {
                    console.log(`Processing image for product: ${title}`);
                    processedImageDataUrl = await window.imageProcessor.downloadAndCompressImage(originalImgUrl);
                    if (processedImageDataUrl) {
                        console.log(`Successfully processed image for: ${title}`);
                        img = processedImageDataUrl; // Use the processed image data URL
                    } else {
                        console.warn(`Failed to process image for: ${title}, using original URL`);
                    }
                } catch (imgError) {
                    console.error(`Error processing image for: ${title}`, imgError);
                }
            }

            // Enhanced debugging for title and basic product info
            console.log(`Processing product ${asin}:`, {
                foundTitle: !!title,
                title: title || 'NO TITLE FOUND',
                foundImg: !!originalImgUrl,
                imgSrc: originalImgUrl || 'NO IMAGE FOUND'
            });

            const priceElement = div.querySelector('span.a-price span.a-offscreen');
            const priceText = priceElement ? priceElement.innerText.trim() : '';
            const numericPrice = priceText ? parseFloat(priceText.replace(/[^0-9.]/g, '')) : 0;

            console.log(`Price extraction for ${asin}:`, {
                foundPriceElement: !!priceElement,
                priceText: priceText || 'NO PRICE FOUND',
                numericPrice
            });

            // Enhanced rating extraction based on actual Amazon structure
            let rating = 0;
            let formattedRating = '';
            
            // Primary rating selector - look for the span with "out of 5 stars" text
            const ratingSpan = div.querySelector('span.a-icon-alt');
            if (ratingSpan) {
                const ratingText = ratingSpan.textContent || '';
                const ratingMatch = ratingText.match(/(\d+(?:\.\d)?)\s*out of 5 stars/i);
                if (ratingMatch) {
                    rating = parseFloat(ratingMatch[1]);
                    if (rating > 0 && rating <= 5) {
                        formattedRating = formatRating(rating);
                    }
                }
            }
            
            // Fallback: try other rating selectors
            if (rating === 0) {
                const fallbackSelectors = [
                    'a[aria-label*="out of 5 stars"]',
                    'span[aria-label*="out of 5 stars"]',
                    '[data-cy="reviews-ratings-slot"]'
                ];
                
                for (const selector of fallbackSelectors) {
                    const element = div.querySelector(selector);
                    if (element) {
                        const ariaLabel = element.getAttribute('aria-label') || element.textContent || '';
                        const ratingMatch = ariaLabel.match(/(\d+(?:\.\d)?)\s*out of 5 stars/i);
                        if (ratingMatch) {
                            rating = parseFloat(ratingMatch[1]);
                            if (rating > 0 && rating <= 5) {
                                formattedRating = formatRating(rating);
                                break;
                            }
                        }
                    }
                }
            }

            // Enhanced review count extraction based on actual Amazon structure
            let reviewCount = 0;
            
            // Primary selector - look for link with "ratings" in aria-label
            const ratingLink = div.querySelector('a[aria-label*="ratings"]');
            if (ratingLink) {
                const ariaLabel = ratingLink.getAttribute('aria-label') || '';
                // Extract number from patterns like "1,598 ratings", "28,908 ratings"
                const reviewMatch = ariaLabel.match(/([\d,]+)\s*ratings?/i);
                if (reviewMatch) {
                    reviewCount = parseInt(reviewMatch[1].replace(/[^0-9]/g, ''), 10) || 0;
                }
            }
            
            // Fallback selectors if primary didn't work
            if (reviewCount === 0) {
                const fallbackSelectors = [
                    'span.a-size-base.s-underline-text',
                    'a[href*="#customerReviews"] span',
                    '.a-size-base-plus',
                    'span.a-size-base',
                    'a.s-underline-text'
                ];
                
                for (const selector of fallbackSelectors) {
                    const reviewElement = div.querySelector(selector);
                    if (reviewElement) {
                        const reviewText = reviewElement.textContent?.trim() || '';
                        // Look for patterns like "1,234", "(1,234)", "1234 reviews", etc.
                        const reviewMatches = reviewText.match(/([\d,]+)/);
                        if (reviewMatches) {
                            const parsedCount = parseInt(reviewMatches[1].replace(/[^0-9]/g, ''), 10);
                            if (parsedCount > 0) {
                                reviewCount = parsedCount;
                                break;
                            }
                        }
                    }
                }
            }

            const primeElement = div.querySelector('i.a-icon-prime');
            const isPrime = !!primeElement;

            const linkElement = div.querySelector('h2 a.a-link-normal');
            const url = linkElement?.href ? new URL(linkElement.href, window.location.origin).toString() : '';

            const availabilityElement = div.querySelector('.a-color-price, .a-color-success, .a-spacing-small .a-spacing-top-small');
            const availability = availabilityElement?.textContent?.trim() || 'In Stock';

            // Enhanced debugging for rating and review scraping
            console.log(`Product ${asin} scraping results:`, {
                title: title?.substring(0, 50) + '...',
                rating: rating,
                ratingFound: rating > 0,
                reviewCount: reviewCount,
                reviewCountFound: reviewCount > 0,
                ratingSource: ratingSpan ? 'span.a-icon-alt' : 'fallback selector',
                reviewSource: ratingLink ? 'aria-label ratings link' : 'fallback selector'
            });

            // Validate that we have minimum required data before adding to products
            if (!title || title.trim() === '' || !asin) {
                console.warn(`Skipping product with ASIN ${asin} - missing title or ASIN:`, {
                    title: title || 'MISSING',
                    asin: asin || 'MISSING'
                });
                continue;
            }

            // Add the product to the list
            products.push({
                asin,
                title,
                price: priceText,
                numericPrice,
                rating,
                formattedRating,
                reviewCount,
                img,
                isPrime,
                searchTerm: searchTerm || 'Unknown',
                url,
                availability,
                // Add a flag to indicate if the image is a data URL
                hasProcessedImage: !!processedImageDataUrl
            });

            // Mark this ASIN as processed for future reference
            processedAsins.add(asin);

        } catch (error) {
            console.error(`Error parsing product (ASIN: ${asin}):`, error);
        }
    }

    console.log(`Found ${products.length} products on search page.`);
    return products;
}

// Auto-scrape when in search results page
async function autoScrapeSearchResults() {
    // Check if we're on a search results page
    if (window.location.pathname.includes('/s') && searchTerm) {
        try {
            console.log('Starting async product scraping...');
            const products = await scrapeSearchResults();

            if (products.length > 0) {
                // Send the products to the background script
                chrome.runtime.sendMessage({
                    action: "productData",
                    products: products,
                    searchTerm: searchTerm
                });
                console.log(`Sent ${products.length} products to background script`);
            }
        } catch (error) {
            console.error('Error in autoScrapeSearchResults:', error);
        }
    }
}

// --- Message Listener ---
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    console.log("Content script received message:", request.action);

    if (request.action === 'scrapeProductDetails') {
        // Call the async function and handle the response
        scrapeProductDetails().then(productData => {
            sendResponse({ success: true, product: productData });
        }).catch(error => {
            console.error('Error scraping product details:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true; // Indicate async response
    } else if (request.action === 'scrapeSearchPage') {
        // Store the search term for future use
        if (request.searchTerm) {
            searchTerm = request.searchTerm;
            chrome.storage.local.set({ currentSearch: { term: searchTerm } });
            console.log("Set current search term to:", searchTerm);
        }

        // If we're already on a search page, scrape it immediately
        if (window.location.pathname.includes('/s')) {
            // Start the scraping process asynchronously
            scrapeSearchResults().then(products => {
                // Send products to background script
                if (products.length > 0) {
                    chrome.runtime.sendMessage({
                        action: "productData",
                        products: products,
                        searchTerm: searchTerm
                    });
                    console.log(`Sent ${products.length} products to background script`);
                }

                // Close tab if requested (for multi-search or user preference)
                if (request.shouldCloseTab !== false) {
                    console.log(`Content: Closing Amazon search tab for term "${searchTerm}" (shouldCloseTab: ${request.shouldCloseTab})`);
                    setTimeout(() => {
                        window.close();
                    }, 1000); // Small delay to ensure data is sent
                } else {
                    console.log(`Content: Keeping Amazon search tab open for term "${searchTerm}" (shouldCloseTab: ${request.shouldCloseTab})`);
                }

                // Try to send a response if the sendResponse is still valid
                try {
                    sendResponse({ success: true, productsCount: products.length, searchTerm });
                } catch (error) {
                    console.warn('Could not send response, likely already timed out:', error);
                }
            }).catch(error => {
                console.error('Error scraping search page:', error);
                
                // Close tab even on error if requested
                if (request.shouldCloseTab !== false) {
                    setTimeout(() => {
                        window.close();
                    }, 1000);
                }
                
                try {
                    sendResponse({ success: false, error: error.message });
                } catch (responseError) {
                    console.warn('Could not send error response:', responseError);
                }
            });

            // Return true to indicate we'll send the response asynchronously
            return true;
        } else {
            // Not on a search page, just acknowledge
            sendResponse({ success: true, message: "Search term stored for future use" });
        }
        return true; // Indicate async response
    } else if (request.type === 'STAYFU_IMPORT_DATA') {
        // Received import data from background script
        console.log("Content script received STAYFU_IMPORT_DATA:", request.items);
        // Forward it to the window/page script
        try {
             console.log("Content script posting message to window:", request.items);
             window.postMessage({ type: "STAYFU_IMPORT_FROM_EXTENSION", data: request.items }, window.location.origin);
             console.log("Content script successfully posted message to window.");
        } catch (error) {
             console.error("Content script ERROR posting message to window:", error);
        }
        // Send acknowledgement back to background script
        sendResponse({ success: true, received: true });
        return false; // Synchronous response here
    } else if (request.type === 'STAYFU_SEARCH_RESULTS') {
        // Received search results from background script
        console.log("Content script received STAYFU_SEARCH_RESULTS:", request.searchTerm, request.products?.length || 0, "products");
        // Forward it to the window/page script
        try {
             console.log("Content script posting search results to window");
             window.postMessage({
                 type: "STAYFU_SEARCH_RESULTS",
                 searchTerm: request.searchTerm,
                 products: request.products
             }, window.location.origin);
             console.log("Content script successfully posted search results to window.");
        } catch (error) {
             console.error("Content script ERROR posting search results to window:", error);
        }
        // Send acknowledgement back to background script
        sendResponse({ success: true, received: true });
        return false; // Synchronous response here
    }

    return false; // Indicate synchronous response for unhandled actions
});

// --- MutationObserver Setup ---
// Callback function to execute when mutations are observed
const observerCallback = (mutationsList, _observer) => {
    let relevantChangeDetected = false;

    for (const mutation of mutationsList) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any added node IS or CONTAINS a product result container
            for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.matches('div[data-component-type="s-search-result"]') ||
                        node.querySelector('div[data-component-type="s-search-result"]')) {
                        relevantChangeDetected = true;
                        break; // Found a relevant change in this mutation's added nodes
                    }
                }
            }
        }

        if (relevantChangeDetected) {
            break; // Found a relevant change in the mutations list
        }
    }

    if (relevantChangeDetected) {
        console.log('Product container changes detected, re-scraping...');
        // Use a small delay to allow potentially complex elements to fully render
        setTimeout(async () => {
            try {
                await autoScrapeSearchResults();
            } catch (error) {
                console.error('Error in mutation observer re-scrape:', error);
            }
        }, 100); // 100ms delay
    }
};

// Create an observer instance linked to the callback function
const observer = new MutationObserver(observerCallback);

// Start observing the body for added nodes and subtree changes
observer.observe(document.body, { childList: true, subtree: true });

console.log('MutationObserver started, observing for added nodes...');

// Scrapes detailed data from a product detail page (DP)
async function scrapeProductDetails() {
    console.log("Scraping product details page.");
    const asinElement = document.querySelector('#ASIN, input[name="ASIN.0"], input[name="ASIN"]');
    const asin = asinElement?.value || window.location.pathname.split('/dp/')[1]?.split('/')[0];

    if (!asin) {
        console.warn("Could not determine ASIN on product page.");
        return null;
    }

    try {
        const title = document.querySelector('#productTitle')?.textContent?.trim() || 'N/A';
        // Try multiple selectors for price
        const priceElement = document.querySelector('.a-price.a-text-price .a-offscreen, #priceblock_ourprice, #priceblock_saleprice, #corePrice_feature_div .a-offscreen, span[data-a-size="xl"][data-a-color="base"] span.a-offscreen');
        const priceText = priceElement?.textContent?.trim() || '';
        const numericPrice = priceText ? parseFloat(priceText.replace(/[^0-9.]/g, '')) : 0;

        const ratingElement = document.querySelector('#acrPopover'); // Container for rating
        const ratingText = ratingElement?.getAttribute('title') || ''; // e.g., "4.5 out of 5 stars"
        const ratingMatch = ratingText.match(/^(\d+(\.\d)?)/);
        const rating = ratingMatch ? parseFloat(ratingMatch[1]) : 0;
        const formattedRating = formatRating(rating);

        const reviewCountElement = document.querySelector('#acrCustomerReviewText');
        const reviewCountText = reviewCountElement?.textContent?.trim() || '0';
        const reviewCount = parseInt(reviewCountText.replace(/[^0-9]/g, ''), 10) || 0;

        // Get the image element and original URL
        const imageElement = document.querySelector('#landingImage, #imgBlkFront'); // Primary image selectors
        const originalImgUrl = imageElement?.src || '';

        // Process the image if available
        let img = originalImgUrl;
        let processedImageDataUrl = null;

        if (originalImgUrl && window.imageProcessor) {
            try {
                console.log(`Processing product detail image for: ${title}`);
                processedImageDataUrl = await window.imageProcessor.downloadAndCompressImage(originalImgUrl, 1200); // Higher quality for product detail
                if (processedImageDataUrl) {
                    console.log(`Successfully processed product detail image`);
                    img = processedImageDataUrl; // Use the processed image data URL
                } else {
                    console.warn(`Failed to process product detail image, using original URL`);
                }
            } catch (imgError) {
                console.error(`Error processing product detail image:`, imgError);
            }
        }

        const primeElement = document.querySelector('#primeDeliveryMessage_feature_div, #mir-layout-DELIVERY_BLOCK i.a-icon-prime'); // Check for Prime indicators
        const isPrime = !!primeElement;

        // Improved availability scraping
        const availabilityElement = document.querySelector('#availability span, #deliveryMessageMirId, #outOfStock');
        const availability = availabilityElement?.textContent?.trim() || 'In Stock';

        // Get bullet points (feature bullets)
        const bulletPoints = Array.from(document.querySelectorAll('#feature-bullets ul.a-unordered-list li span.a-list-item'))
            .map(bullet => bullet.textContent?.trim())
            .filter(text => text && text.length > 0);

        // Get product description
        const descriptionElement = document.querySelector('#productDescription');
        const description = descriptionElement?.textContent?.trim() || '';

        // Get specifications (often in a table)
        const specifications = {};
        const specTableRows = document.querySelectorAll('#productDetails_detailBullets_sections1 tr, #detailBullets_feature_div .detail-bullet-list li');
        specTableRows.forEach(row => {
            const keyElement = row.querySelector('th, .a-list-item > .a-text-bold');
            const valueElement = row.querySelector('td, .a-list-item > span:not(.a-text-bold)');
            if (keyElement && valueElement) {
                const key = keyElement.textContent?.trim().replace(/:$/, ''); // Remove trailing colon
                const value = valueElement.textContent?.trim();
                if (key && value) {
                    specifications[key] = value;
                }
            }
        });

        const productData = {
            asin,
            title,
            price: priceText,
            numericPrice,
            rating,
            formattedRating,
            reviewCount,
            img,
            isPrime,
            url: window.location.href,
            availability,
            details: {
                bullet_points: bulletPoints,
                description,
                specifications
            },
            // Add a flag to indicate if the image is a data URL
            hasProcessedImage: !!processedImageDataUrl
        };
        console.log("Product details scraped:", productData);
        return productData;

    } catch (error) {
        console.error(`Error parsing product details (ASIN: ${asin}):`, error);
        return null;
    }
}

// Run the initial scrape when the script loads
setTimeout(async () => {
    console.log('Running initial product scrape');
    try {
        await autoScrapeSearchResults();
    } catch (error) {
        console.error('Error in initial product scrape:', error);
    }
}, 1000); // Slight delay to ensure page is fully loaded
