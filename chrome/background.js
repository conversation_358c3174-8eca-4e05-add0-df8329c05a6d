// background.js (Refactored for Manifest V3) - Version 2.0.2
console.log('StayFu Extension Background Script Loaded - Version 2.0.2');

// --- Configuration & State ---
let stayfuAppUrl = 'https://stayfu.com';
let stayfuApiToken = null;
let isSearching = false;

// Handle auto-configuration from web app
async function handleAutoConfigureExtension(request, sendResponse) {
    const { url, token } = request;
    console.log('BG: Auto-configuring extension with URL:', url);

    try {
        if (!url || !token) {
            throw new Error('URL and token are required for auto-configuration');
        }

        // Set URL
        const formattedUrl = formatBaseUrl(url);
        if (!formattedUrl) {
            throw new Error('Invalid URL format');
        }

        // Save both URL and token
        await chrome.storage.local.set({ 
            stayfuAppUrl: formattedUrl,
            stayfuApiToken: token 
        });
        
        stayfuAppUrl = formattedUrl;
        stayfuApiToken = token;

        console.log('BG: Extension auto-configured successfully');
        sendResponse({
            success: true,
            message: 'Extension configured automatically'
        });
    } catch (error) {
        console.error('BG: Error auto-configuring extension:', error);
        sendResponse({
            success: false,
            error: error.message || 'Failed to auto-configure extension'
        });
    }
}

// Additional state variables
let searchResults = []; // In-memory cache of current search results
let currentSearchTerm = ''; // Current search term

// URL fallback order
const URL_FALLBACKS = [
    'https://stayfu.com',
    'https://stage.stayfu.com', 
    'https://dev.stayfu.com',
    'http://localhost:8080'
];

// --- Helper Functions ---
// Test URL connectivity with timeout
async function testUrlConnectivity(url, timeout = 5000) {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        const response = await fetch(`${url}/api/health`, {
            method: 'GET',
            signal: controller.signal,
            mode: 'cors'
        });
        
        clearTimeout(timeoutId);
        return response.ok;
    } catch (error) {
        console.log(`URL ${url} not reachable:`, error.message);
        return false;
    }
}

// Find working StayFu URL with fallbacks
async function findWorkingStayFuUrl() {
    console.log('BG: Testing URL connectivity with fallbacks...');
    
    for (const url of URL_FALLBACKS) {
        console.log(`BG: Testing ${url}...`);
        const isReachable = await testUrlConnectivity(url);
        
        if (isReachable) {
            console.log(`BG: Found working URL: ${url}`);
            stayfuAppUrl = url;
            // Save the working URL
            chrome.storage.local.set({ stayfuAppUrl: url });
            return url;
        }
    }
    
    console.warn('BG: No StayFu URLs are reachable, using default');
    stayfuAppUrl = URL_FALLBACKS[0]; // Default to first option
    return stayfuAppUrl;
}

function formatBaseUrl(url) {
    if (!url) return null;

    try {
        // If the URL doesn't start with a protocol, add https:// by default
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            // Prefer https for production domains
            if (url.includes('stayfu.com') || url.includes('stayfuse.com')) {
                url = `https://${url}`;
            } else {
                url = `http://${url}`; // Use http for localhost
            }
        }

        // Try to construct a URL object to validate
        const urlObj = new URL(url);

        // Special handling for stayfu.com and stayfuse.com domains
        const hostname = urlObj.hostname.toLowerCase();

        // Check for common domain typos and fix them
        if (hostname === 'stayfuse.com' || hostname.endsWith('.stayfuse.com')) {
            console.log('BG: Correcting stayfuse.com to stayfu.com');
            // Replace stayfuse.com with stayfu.com
            urlObj.hostname = urlObj.hostname.replace('stayfuse.com', 'stayfu.com');
        }

        // Return the formatted URL including protocol, hostname, and port if present
        return urlObj.toString().replace(/\/$/, ''); // Remove trailing slash if present
    } catch (error) {
        console.error('BG: Invalid URL format:', error);
        return null;
    }
}

function formatProductData(product) {
    return {
        name: product.title || 'N/A',
        sku: product.asin || `UNKNOWN_${Date.now()}`,
        image_url: product.img || '',
        price: parseFloat(product.numericPrice) || 0,
        supplier: 'Amazon',
        supplier_url: product.url || `https://www.amazon.com/dp/${product.asin}`,
        features: product.details?.bullet_points || [],
        specifications: product.details?.specifications || {},
        external_id: product.asin,
        // Add a flag to indicate if the image is a processed data URL
        has_processed_image: !!product.hasProcessedImage,
        metadata: {
            rating: product.rating,
            reviewCount: product.reviewCount,
            isPrime: product.isPrime,
            availability: product.availability,
            searchTerm: product.searchTerm
        }
    };
}

// --- Initialization ---
async function loadConfig() {
    try {
        const result = await chrome.storage.local.get(['stayfuAppUrl', 'stayfuApiToken']);

        // If we have a stored URL, try to use it, otherwise find a working one
        if (result.stayfuAppUrl) {
            const formattedUrl = formatBaseUrl(result.stayfuAppUrl);
            if (formattedUrl) {
                console.log(`BG: Testing stored URL: ${formattedUrl}`);
                const isReachable = await testUrlConnectivity(formattedUrl);
                if (isReachable) {
                    stayfuAppUrl = formattedUrl;
                    console.log(`BG: Using stored URL: ${stayfuAppUrl}`);
                } else {
                    console.log('BG: Stored URL not reachable, finding alternative...');
                    await findWorkingStayFuUrl();
                }
            } else {
                console.error('BG: Invalid URL in storage, finding working URL...');
                await findWorkingStayFuUrl();
            }
        } else {
            console.log('BG: No stored URL, finding working StayFu URL...');
            await findWorkingStayFuUrl();
        }

        // Validate token
        stayfuApiToken = result.stayfuApiToken || null;
        if (stayfuApiToken) {
            if (typeof stayfuApiToken !== 'string' || stayfuApiToken.trim() === '') {
                console.error('BG: Invalid token format in storage');
                stayfuApiToken = null;
                // Clean up invalid token
                await chrome.storage.local.remove(['stayfuApiToken', 'stayfuTokenHash']);
            }
        }

        console.log('BG: StayFu config loaded:', {
            url: stayfuAppUrl,
            hasToken: !!stayfuApiToken,
            tokenPrefix: stayfuApiToken ? stayfuApiToken.substring(0, 10) + '...' : 'none'
        });
    } catch (error) {
        console.error("BG: Error loading config:", error);
        stayfuApiToken = null; // Reset token on error
        stayfuAppUrl = 'http://localhost:8080'; // Reset to default URL
    }
}

// Handle getting StayFu settings
async function handleGetStayfuSettings(_request, sendResponse) {
    try {
        sendResponse({
            url: stayfuAppUrl,
            hasToken: !!stayfuApiToken
        });
    } catch (error) {
        console.error('BG: Error getting settings:', error);
        sendResponse({ error: error.message });
    }
}

// Handle checking StayFu connection
async function handleCheckStayfuConnection(_request, sendResponse) {
  try {
    // Simple check - just verify we have the required configuration
    const data = await chrome.storage.local.get(['stayfuApiToken', 'stayfuAppUrl']);
    
    if (!data.stayfuAppUrl) {
      sendResponse({
        connected: false,
        error: 'StayFu URL not configured. Please set the URL in extension settings.'
      });
      return;
    }

    if (!data.stayfuApiToken) {
      sendResponse({
        connected: false,
        error: 'API token not configured. Please set the token in extension settings.'
      });
      return;
    }

    // If we have both URL and token, consider it connected
    // The actual communication works via window messages, not API calls
    sendResponse({
      connected: true,
      message: 'StayFu extension is configured and ready to use'
    });

  } catch (error) {
    console.error('BG: Error checking connection:', error);
    sendResponse({
      connected: false,
      error: error.message || 'Connection check failed'
    });
  }
}

// Handle setting StayFu URL
async function handleSetStayfuUrl(request, sendResponse) {
    const url = request.url;
    console.log('BG: Setting StayFu URL:', url);

    try {
        const formattedUrl = formatBaseUrl(url);
        if (!formattedUrl) {
            throw new Error('Invalid URL format');
        }

        await chrome.storage.local.set({ stayfuAppUrl: formattedUrl });
        stayfuAppUrl = formattedUrl;
        sendResponse({ success: true });
    } catch (error) {
        console.error('BG: Error setting StayFu URL:', error);
        sendResponse({
            success: false,
            error: error.message || 'Failed to save URL'
        });
    }
}

// Handle setting StayFu token
async function handleSetStayfuToken(request, sendResponse) {
    const token = request.token;
    console.log('BG: Setting StayFu token');

    try {
        if (!token || typeof token !== 'string' || token.trim() === '') {
            throw new Error('Invalid token format');
        }

        // Save the token directly - validation will happen when actually used
        await chrome.storage.local.set({ stayfuApiToken: token });
        stayfuApiToken = token;

        console.log('BG: Token saved successfully');
        sendResponse({
            success: true,
            message: 'Token saved successfully'
        });
    } catch (error) {
        console.error('BG: Error setting StayFu token:', error);
        sendResponse({
            success: false,
            error: error.message || 'Failed to validate token'
        });
    }
}

// Handle product data received from content script
function handleProductData(request, _sender, sendResponse) {
    console.log(`BG: Received product data for "${request.searchTerm}":`,
               `${request.products.length} products`);

    // Append new results to existing ones instead of overwriting
    if (request.products && request.products.length > 0) {
        // Add search term to each product for identification
        const productsWithSearchTerm = request.products.map(product => ({
            ...product,
            searchTerm: request.searchTerm,
            timestamp: Date.now()
        }));
        
        // Append to existing results
        searchResults = [...searchResults, ...productsWithSearchTerm];
        console.log(`BG: Total accumulated products: ${searchResults.length}`);
        
        // Send the results back to the app
        sendSearchResultsToApp(request.searchTerm, productsWithSearchTerm);
    }

    // No response needed
    if (sendResponse) {
        sendResponse({ success: true });
    }
}

// Send search results back to the app
async function sendSearchResultsToApp(searchTerm, products) {
    try {
        console.log(`BG: Sending search results to app: ${products.length} products for "${searchTerm}"`);

        // Method 1: Try to find the app tab and use executeScript to post a message
        const tabs = await chrome.tabs.query({ url: [
            `${stayfuAppUrl}/*`,
            'http://localhost:*/*',
            'http://127.0.0.1:*/*',
            'https://*.stayfu.com/*',
            'https://stayfu.com/*',
            'https://stage.stayfu.com/*'
        ]});

        // Method 2: Use external messaging to send results directly to the app
        // This is more reliable than the window.postMessage approach
        try {
            // Find all tabs that might contain our app
            if (tabs.length > 0) {
                for (const tab of tabs) {
                    try {
                        // Try to send a message to the app in this tab
                        console.log(`BG: Trying to send search results to app in tab ${tab.id}`);

                        // First try direct external messaging to the app
                        const appOrigin = new URL(tab.url).origin;
                        console.log(`BG: Sending external message to ${appOrigin}`);

                        // Use content script to post a message to the window
                        chrome.tabs.sendMessage(tab.id, {
                            type: 'STAYFU_SEARCH_RESULTS',
                            searchTerm,
                            products
                        }, (_response) => {
                            if (chrome.runtime.lastError) {
                                console.warn(`BG: Error sending message to tab ${tab.id}:`, chrome.runtime.lastError);

                                // Fallback to injecting script
                                try {
                                    chrome.scripting.executeScript({
                                        target: { tabId: tab.id },
                                        func: (searchTermArg, productsArg) => {
                                            console.log(`Injected script posting message with ${productsArg.length} products`);
                                            window.postMessage({
                                                type: 'STAYFU_SEARCH_RESULTS',
                                                searchTerm: searchTermArg,
                                                products: productsArg
                                            }, window.location.origin);
                                        },
                                        args: [searchTerm, products]
                                    });
                                } catch (scriptError) {
                                    console.error(`BG: Error executing script in tab ${tab.id}:`, scriptError);
                                }
                            } else {
                                console.log(`BG: Successfully sent search results to tab ${tab.id}`);
                            }
                        });
                    } catch (tabError) {
                        console.error(`BG: Error processing tab ${tab.id}:`, tabError);
                    }
                }
            } else {
                console.warn('BG: No StayFu app tabs found to send results to');
            }
        } catch (error) {
            console.error('BG: Error sending results via tabs:', error);
        }

        // Update search state
        isSearching = false;

    } catch (error) {
        console.error('BG: Error sending search results to app:', error);
        isSearching = false;
    }
}

// Handle starting a search
// Handle multiple search terms sequentially
async function processMultipleSearchTerms(searchTerms, sendResponse) {
    console.log(`BG: Processing ${searchTerms.length} search terms sequentially`);
    
    try {
        sendResponse({
            success: true,
            message: `Search initiated for ${searchTerms.length} terms`
        });

        for (let i = 0; i < searchTerms.length; i++) {
            const term = searchTerms[i];
            console.log(`BG: Processing term ${i + 1}/${searchTerms.length}: "${term}"`);
            
            try {
                // Wait for this search to complete before starting the next one
                // All tabs should close after their individual scraping is complete
                await processSingleSearchTerm(term, null, true);
                console.log(`BG: Completed search term ${i + 1}/${searchTerms.length}: "${term}"`);
            } catch (error) {
                console.error(`BG: Error processing search term "${term}":`, error);
                // Continue with next term even if this one failed
            }
            
            // Add delay between searches to avoid rate limiting
            if (i < searchTerms.length - 1) {
                console.log(`BG: Waiting 2 seconds before next search...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log(`BG: Completed all ${searchTerms.length} search terms`);
    } catch (error) {
        console.error('BG: Error in multi-term search:', error);
    } finally {
        isSearching = false;
    }
}

// Handle single search term
async function processSingleSearchTerm(searchTerm, sendResponse = null, shouldCloseTab = true) {
    const searchUrl = `https://www.amazon.com/s?k=${encodeURIComponent(searchTerm)}`;
    
    console.log(`BG: Creating tab for search term: "${searchTerm}"`);

    // Open the search in a new tab
    const newTab = await chrome.tabs.create({ url: searchUrl, active: false });
    
    console.log(`BG: Created tab ${newTab.id} for search term: "${searchTerm}"`);

    // Create a promise that resolves when scraping is complete
    return new Promise((resolve, reject) => {
        // Set a timeout to prevent hanging
        const timeout = setTimeout(() => {
            chrome.tabs.onUpdated.removeListener(listener);
            console.warn(`BG: Timeout waiting for tab ${newTab.id} to complete loading`);
            // Close tab on timeout if shouldCloseTab is true
            if (shouldCloseTab) {
                chrome.tabs.remove(newTab.id).catch(err => 
                    console.warn(`BG: Error closing tab ${newTab.id} on timeout:`, err)
                );
            }
            resolve();
        }, 30000); // 30 second timeout

        // Tell the content script to scrape this page when it loads
        function listener(tabId, changeInfo, _tab) {
            if (tabId === newTab.id && changeInfo.status === 'complete') {
                console.log(`BG: Tab ${newTab.id} completed loading, starting scrape for: "${searchTerm}"`);
                
                // Remove the listener to avoid multiple calls
                chrome.tabs.onUpdated.removeListener(listener);
                clearTimeout(timeout);

                // Send message to content script to scrape the page
                chrome.tabs.sendMessage(tabId, {
                    action: 'scrapeSearchPage',
                    searchTerm,
                    shouldCloseTab
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error(`BG: Error sending scrape message to tab ${tabId}:`, chrome.runtime.lastError);
                        // Close tab on error if shouldCloseTab is true
                        if (shouldCloseTab) {
                            console.log(`BG: Closing tab ${tabId} due to error`);
                            chrome.tabs.remove(tabId).catch(err => 
                                console.warn(`BG: Error closing tab ${tabId} on error:`, err)
                            );
                        }
                        reject(chrome.runtime.lastError);
                    } else {
                        console.log(`BG: Scrape initiated for tab ${tabId}, search term: "${searchTerm}", shouldCloseTab: ${shouldCloseTab}`);
                        // Note: Tab will be closed by content script after scraping is complete
                        resolve(response);
                    }
                });
            }
        }
        
        chrome.tabs.onUpdated.addListener(listener);
    });

    // Handle sendResponse if provided
    if (sendResponse) {
        sendResponse({
            success: true,
            message: `Search initiated for "${searchTerm}"`
        });
    }
}

// Auto-configuration handler
async function handleAutoConfig(request, sendResponse) {
    try {
        const { config } = request;
        
        if (!config || !config.url || !config.token) {
            throw new Error('Invalid configuration data');
        }

        console.log('BG: Auto-configuring extension with:', config.url);
        
        // Save configuration
        await chrome.storage.local.set({
            stayfuAppUrl: config.url,
            stayfuApiToken: config.token
        });

        // Update global variables
        stayfuAppUrl = config.url;
        stayfuApiToken = config.token;

        console.log('BG: Auto-configuration completed successfully');
        
        sendResponse({
            success: true,
            message: 'Extension configured successfully'
        });
    } catch (error) {
        console.error('BG: Error in auto-configuration:', error);
        sendResponse({
            success: false,
            error: error.message || 'Auto-configuration failed'
        });
    }
}

async function handleStartSearch(request, sendResponse) {
    try {
        const searchTerms = request.searchTerms || [request.searchTerm];
        const searchTerm = searchTerms[0]; // Use first term for backward compatibility
        
        if (!searchTerm) {
            throw new Error('No search term provided');
        }

        console.log(`BG: Starting search for ${searchTerms.length} term(s):`, searchTerms);

        // Update state
        isSearching = true;
        currentSearchTerm = searchTerm;
        searchResults = [];

        // Handle multiple search terms
        if (searchTerms.length > 1) {
            // Process multiple terms sequentially
            await processMultipleSearchTerms(searchTerms, sendResponse);
        } else {
            // Single term processing (existing logic) - close tab after scraping
            await processSingleSearchTerm(searchTerm, sendResponse, true);
        }
    } catch (error) {
        console.error('BG: Error starting search:', error);
        isSearching = false;
        sendResponse({
            success: false,
            error: error.message || 'Failed to start search'
        });
    }
}

// Handle getting search status
function handleGetSearchStatus(_request, sendResponse) {
    sendResponse({
        isSearching,
        currentSearchTerm,
        resultsCount: searchResults.length
    });
}

// Handle getting search results
function handleGetSearchResults(_request, sendResponse) {
    sendResponse({
        success: true,
        searchTerm: currentSearchTerm,
        products: searchResults
    });
}

// Handle getting extension info for announcements
function handleGetExtensionInfo(_request, sendResponse) {
    // Check if we have a valid connection to StayFu
    chrome.storage.local.get(['stayfuApiToken', 'stayfuAppUrl'], (data) => {
        const isConnected = !!(data.stayfuApiToken && data.stayfuAppUrl);
        
        sendResponse({
            success: true,
            connected: isConnected,
            version: '2.0.2',
            extensionId: chrome.runtime.id,
            appUrl: data.stayfuAppUrl || null,
            hasToken: !!data.stayfuApiToken
        });
    });
    
    return true; // Indicates async response
}

// Handle updating property data
function handleUpdatePropertyData(request, sendResponse) {
    console.log('BG: Handling updatePropertyData', request.propertyData);

    // Store the property data in local storage for later use
    chrome.storage.local.set({
        propertyData: request.propertyData
    }, () => {
        if (chrome.runtime.lastError) {
            console.error('BG: Error storing property data:', chrome.runtime.lastError);
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            console.log('BG: Property data stored successfully');
            sendResponse({ success: true });
        }
    });
}

// --- External Message Handling ---
// Handle external connection messages from web pages/apps
chrome.runtime.onMessageExternal.addListener(
  (request, sender, sendResponse) => {
    console.log('BG: Received external message:', request, 'from:', sender.url);
    const originUrl = sender.url ? new URL(sender.url).origin : null;

    // Check if the sender is from a trusted origin
    if (!originUrl) {
      console.error('BG: Rejected message from unknown origin');
      sendResponse({ success: false, error: 'Unknown origin' });
      return;
    }

    // Check if the origin matches our app URL or localhost
    const isLocalhost = originUrl.startsWith('http://localhost:') || originUrl.startsWith('http://127.0.0.1:');

    // More flexible domain checking for production domains
    const isStayfuDomain = originUrl.includes('stayfu.com') || originUrl.includes('stayfuse.com');
    const isStayfuApp = originUrl === stayfuAppUrl || isStayfuDomain;

    if (!isLocalhost && !isStayfuApp) {
      console.error(`BG: Rejected message from untrusted origin: ${originUrl}`);
      sendResponse({ success: false, error: 'Untrusted origin' });
      return;
    }

    // Log the trusted origin
    console.log(`BG: Accepted message from trusted origin: ${originUrl} (isLocalhost: ${isLocalhost}, isStayfuDomain: ${isStayfuDomain})`);

    console.log(`BG: Processing external message from trusted origin: ${originUrl}`);

    // Handle different message types
    if (request.action === 'ping') {
      console.log('BG: External ping request received');
      sendResponse({
        success: true,
        version: chrome.runtime.getManifest().version,
        extensionId: chrome.runtime.id
      });
      return;
    } else if (request.action === 'startSearch') {
      console.log('BG: External startSearch request received');

      // Call the internal search handler
      handleStartSearch(request, sendResponse);
      return true; // Indicate async response
    } else if (request.action === 'autoConfig') {
      console.log('BG: Auto-config request received');

      // Call the auto-config handler
      handleAutoConfig(request, sendResponse);
      return true; // Indicate async response
    } else if (request.action === 'getSearchResults') {
      console.log('BG: External getSearchResults request received');

      // Call the internal handler
      handleGetSearchResults(request, sendResponse);
      return false; // Synchronous response
    } else if (request.action === 'checkStayfuConnection') {
      console.log('BG: External checkStayfuConnection request received');

      // Call the internal handler
      handleCheckStayfuConnection(request, sendResponse);
      return true; // Asynchronous response
    } else if (request.action === 'updatePropertyData') {
      console.log('BG: External updatePropertyData request received');

      // Call the internal handler
      handleUpdatePropertyData(request, sendResponse);
      return true; // Asynchronous response
    }

    // Unknown action
    console.warn(`BG: Unhandled external action: ${request.action}`);
    sendResponse({ success: false, error: `Unknown action: ${request.action}` });
  }
);

// Open settings page on icon click
chrome.action.onClicked.addListener(() => {
    chrome.runtime.openOptionsPage();
});

// --- Message Listener ---
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    const action = request?.action;
    console.log(`BG: Received action: "${action}"`);

    // Special case for product data from content script
    if (action === 'productData') {
        handleProductData(request, sender, sendResponse);
        return true;
    }

    if (action === 'ping') {
        console.log('BG: Responding to ping from:', sender.url);
        if (!sender.url) {
            console.warn('BG: Ping received from unknown origin');
            sendResponse({ success: false, error: 'Invalid origin' });
            return true;
        }

        try {
            const response = {
                success: true,
                version: '2.0.1',
                extensionId: chrome.runtime.id,
                manifest: chrome.runtime.getManifest()
            };
            console.log('BG: Ping response:', response);
            sendResponse(response);
        } catch (error) {
            console.error('BG: Ping response error:', error);
            sendResponse({
                success: false,
                error: error.message,
                extensionId: chrome.runtime.id
            });
        }
        return true;
    }

    let handlerFunction = null;
    let isAsync = false;

    // Map actions to handler functions
    if (action === 'getStayfuSettings') {
        handlerFunction = handleGetStayfuSettings;
        isAsync = false; // It's synchronous
    } else if (action === 'checkStayfuConnection') {
        handlerFunction = handleCheckStayfuConnection;
        isAsync = true; // It's asynchronous
    } else if (action === 'setStayfuUrl') {
        handlerFunction = handleSetStayfuUrl;
        isAsync = true; // It's asynchronous
    } else if (action === 'setStayfuToken') {
        handlerFunction = handleSetStayfuToken;
        isAsync = true; // It's asynchronous
    } else if (action === 'autoConfigureExtension') {
        handlerFunction = handleAutoConfigureExtension;
        isAsync = true; // It's asynchronous
    } else if (action === 'startSearch') {
        handlerFunction = handleStartSearch;
        isAsync = true; // It's asynchronous
    } else if (action === 'autoConfig') {
        handlerFunction = handleAutoConfig;
        isAsync = true; // It's asynchronous
    } else if (action === 'getSearchStatus') {
        handlerFunction = handleGetSearchStatus;
        isAsync = false; // It's synchronous
    } else if (action === 'getSearchResults') {
        handlerFunction = handleGetSearchResults;
        isAsync = false; // It's synchronous
    } else if (action === 'getExtensionInfo') {
        handlerFunction = handleGetExtensionInfo;
        isAsync = true; // It's asynchronous
    }

    console.log(`BG: Checking handler for action "${action}". Handler assigned: ${typeof handlerFunction === 'function'}`); // Add log
    if (typeof handlerFunction === 'function') {
        console.log(`BG: Executing handler for action "${action}".`);
        try {
            const result = handlerFunction(request, sendResponse);
            if (!isAsync && result !== undefined) {
                console.warn(`BG: Sync handler for "${action}" returned a value directly.`);
            }
        } catch (error) {
            console.error(`BG: Error executing handler for action "${action}":`, error);
            try {
                sendResponse({ success: false, error: error.message || 'An unexpected error occurred' });
            } catch (e) {
                console.error("BG: Error calling sendResponse:", e);
            }
            isAsync = false;
        }
        return isAsync; // Return true for async handlers
    } else {
        console.warn(`BG: No handler found for action "${action}"`);
        try {
            sendResponse({ success: false, error: `Unknown action: ${action}` });
        } catch (e) {
            console.error("BG: Error calling sendResponse for unknown action:", e);
        }
        return false;
    }
});

// --- Initialization ---
// Load configuration on startup
loadConfig();

// Clear any previous search data
chrome.storage.local.remove(['searchMeta', 'products']);
