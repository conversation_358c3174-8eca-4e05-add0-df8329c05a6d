// Test script to validate Chrome extension functionality
console.log('Testing StayFu Chrome Extension...');

// Test 1: Check if Chrome APIs are available
if (typeof chrome !== 'undefined') {
    console.log('✅ Chrome APIs available');
    
    // Test 2: Check manifest
    if (chrome.runtime && chrome.runtime.getManifest) {
        const manifest = chrome.runtime.getManifest();
        console.log('✅ Extension manifest loaded:', manifest.name, 'v' + manifest.version);
    }
    
    // Test 3: Check storage
    if (chrome.storage) {
        console.log('✅ Chrome storage API available');
    }
    
    // Test 4: Check tabs API
    if (chrome.tabs) {
        console.log('✅ Chrome tabs API available');
    }
    
    console.log('Extension validation complete!');
} else {
    console.log('❌ Chrome APIs not available - extension not loaded');
}
