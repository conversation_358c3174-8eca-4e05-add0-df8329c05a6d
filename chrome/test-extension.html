<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test</title>
</head>
<body>
    <h1>StayFu Extension Test</h1>
    <div id="status">Testing...</div>
    <button id="testButton">Test Extension Detection</button>
    
    <script>
        console.log('Test page loaded');
        
        let extensionDetected = false;
        
        // Listen for extension announcements
        window.addEventListener('message', (event) => {
            if (event.data.type === 'STAYFU_EXTENSION_ANNOUNCEMENT') {
                extensionDetected = true;
                console.log('✅ Extension detected:', event.data);
                document.getElementById('status').innerHTML = `
                    <strong>✅ Extension Found!</strong><br>
                    ID: ${event.data.extensionId}<br>
                    Version: ${event.data.version}<br>
                    Status: ${event.data.status || 'connected'}<br>
                    Connected: ${event.data.connected}<br>
                    Timestamp: ${new Date(event.data.timestamp).toLocaleTimeString()}
                `;
            }
        });
        
        // Test function
        function testExtension() {
            console.log('Requesting extension announcement...');
            document.getElementById('status').textContent = 'Requesting extension announcement...';
            
            extensionDetected = false;
            
            // Request extension to announce itself
            window.postMessage({
                type: 'STAYFU_REQUEST_EXTENSION_ANNOUNCEMENT'
            }, '*');
            
            // Check result after timeout
            setTimeout(() => {
                if (!extensionDetected) {
                    console.log('❌ No extension detected');
                    document.getElementById('status').innerHTML = '<strong style="color: red;">❌ No Extension Detected</strong>';
                }
            }, 2000);
        }
        
        // Auto-test on page load
        setTimeout(testExtension, 1000);
        
        // Manual test button
        document.getElementById('testButton').addEventListener('click', testExtension);
    </script>
</body>
</html>
