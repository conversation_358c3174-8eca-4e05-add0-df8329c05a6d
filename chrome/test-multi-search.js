// Test Multi-Search Functionality
console.log('Testing Multi-Search Chrome Extension...');

// Simulate multi-search request
function testMultiSearch() {
    const searchTerms = ['laptop', 'wireless mouse'];
    
    console.log(`Testing search with terms: ${searchTerms.join(', ')}`);
    
    // This would normally be sent to the extension
    const message = {
        action: 'startSearch',
        searchTerm: searchTerms.join(', ') // Comma-separated
    };
    
    console.log('Message that would be sent to extension:', message);
    
    // The extension should:
    // 1. Parse the comma-separated terms
    // 2. Create tabs for each term sequentially
    // 3. Wait for each tab to load
    // 4. Send scrape command to each tab
    // 5. Close tabs after scraping (except the last one, depending on config)
    
    console.log('Expected behavior:');
    console.log('1. Split "laptop, wireless mouse" into ["laptop", "wireless mouse"]');
    console.log('2. Create tab 1 for "laptop" search');
    console.log('3. Wait for tab 1 to load and scrape');
    console.log('4. Wait 2 seconds');
    console.log('5. Create tab 2 for "wireless mouse" search');
    console.log('6. Wait for tab 2 to load and scrape');
    console.log('7. Close both tabs after scraping');
}

testMultiSearch();
